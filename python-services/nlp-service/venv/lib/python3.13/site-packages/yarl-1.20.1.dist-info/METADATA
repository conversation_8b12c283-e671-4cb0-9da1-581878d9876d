Metadata-Version: 2.4
Name: yarl
Version: 1.20.1
Summary: Yet another URL library
Home-page: https://github.com/aio-libs/yarl
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: aiohttp team <<EMAIL>>
Maintainer-email: <EMAIL>
License: Apache-2.0
Project-URL: Chat: Matrix, https://matrix.to/#/#aio-libs:matrix.org
Project-URL: Chat: Matrix Space, https://matrix.to/#/#aio-libs-space:matrix.org
Project-URL: CI: GitHub Workflows, https://github.com/aio-libs/yarl/actions?query=branch:master
Project-URL: Code of Conduct, https://github.com/aio-libs/.github/blob/master/CODE_OF_CONDUCT.md
Project-URL: Coverage: codecov, https://codecov.io/github/aio-libs/yarl
Project-URL: Docs: Changelog, https://yarl.aio-libs.org/en/latest/changes/
Project-URL: Docs: RTD, https://yarl.aio-libs.org
Project-URL: GitHub: issues, https://github.com/aio-libs/yarl/issues
Project-URL: GitHub: repo, https://github.com/aio-libs/yarl
Keywords: cython,cext,yarl
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Cython
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: NOTICE
Requires-Dist: idna>=2.0
Requires-Dist: multidict>=4.0
Requires-Dist: propcache>=0.2.1
Dynamic: license-file

yarl
====

The module provides handy URL class for URL parsing and changing.

.. image:: https://github.com/aio-libs/yarl/workflows/CI/badge.svg
   :target: https://github.com/aio-libs/yarl/actions?query=workflow%3ACI
   :align: right

.. image:: https://codecov.io/gh/aio-libs/yarl/graph/badge.svg?flag=pytest
   :target: https://app.codecov.io/gh/aio-libs/yarl?flags[]=pytest
   :alt: Codecov coverage for the pytest-driven measurements

.. image:: https://img.shields.io/endpoint?url=https://codspeed.io/badge.json
   :target: https://codspeed.io/aio-libs/yarl

.. image:: https://badge.fury.io/py/yarl.svg
   :target: https://badge.fury.io/py/yarl

.. image:: https://readthedocs.org/projects/yarl/badge/?version=latest
   :target: https://yarl.aio-libs.org

.. image:: https://img.shields.io/pypi/pyversions/yarl.svg
   :target: https://pypi.python.org/pypi/yarl

.. image:: https://img.shields.io/matrix/aio-libs:matrix.org?label=Discuss%20on%20Matrix%20at%20%23aio-libs%3Amatrix.org&logo=matrix&server_fqdn=matrix.org&style=flat
   :target: https://matrix.to/#/%23aio-libs:matrix.org
   :alt: Matrix Room — #aio-libs:matrix.org

.. image:: https://img.shields.io/matrix/aio-libs-space:matrix.org?label=Discuss%20on%20Matrix%20at%20%23aio-libs-space%3Amatrix.org&logo=matrix&server_fqdn=matrix.org&style=flat
   :target: https://matrix.to/#/%23aio-libs-space:matrix.org
   :alt: Matrix Space — #aio-libs-space:matrix.org


Introduction
------------

Url is constructed from ``str``:

.. code-block:: pycon

   >>> from yarl import URL
   >>> url = URL('https://www.python.org/~guido?arg=1#frag')
   >>> url
   URL('https://www.python.org/~guido?arg=1#frag')

All url parts: *scheme*, *user*, *password*, *host*, *port*, *path*,
*query* and *fragment* are accessible by properties:

.. code-block:: pycon

   >>> url.scheme
   'https'
   >>> url.host
   'www.python.org'
   >>> url.path
   '/~guido'
   >>> url.query_string
   'arg=1'
   >>> url.query
   <MultiDictProxy('arg': '1')>
   >>> url.fragment
   'frag'

All url manipulations produce a new url object:

.. code-block:: pycon

   >>> url = URL('https://www.python.org')
   >>> url / 'foo' / 'bar'
   URL('https://www.python.org/foo/bar')
   >>> url / 'foo' % {'bar': 'baz'}
   URL('https://www.python.org/foo?bar=baz')

Strings passed to constructor and modification methods are
automatically encoded giving canonical representation as result:

.. code-block:: pycon

   >>> url = URL('https://www.python.org/шлях')
   >>> url
   URL('https://www.python.org/%D1%88%D0%BB%D1%8F%D1%85')

Regular properties are *percent-decoded*, use ``raw_`` versions for
getting *encoded* strings:

.. code-block:: pycon

   >>> url.path
   '/шлях'

   >>> url.raw_path
   '/%D1%88%D0%BB%D1%8F%D1%85'

Human readable representation of URL is available as ``.human_repr()``:

.. code-block:: pycon

   >>> url.human_repr()
   'https://www.python.org/шлях'

For full documentation please read https://yarl.aio-libs.org.


Installation
------------

::

   $ pip install yarl

The library is Python 3 only!

PyPI contains binary wheels for Linux, Windows and MacOS.  If you want to install
``yarl`` on another operating system where wheels are not provided,
the tarball will be used to compile the library from
the source code. It requires a C compiler and and Python headers installed.

To skip the compilation you must explicitly opt-in by using a PEP 517
configuration setting ``pure-python``, or setting the ``YARL_NO_EXTENSIONS``
environment variable to a non-empty value, e.g.:

.. code-block:: console

   $ pip install yarl --config-settings=pure-python=false

Please note that the pure-Python (uncompiled) version is much slower. However,
PyPy always uses a pure-Python implementation, and, as such, it is unaffected
by this variable.

Dependencies
------------

YARL requires multidict_ and propcache_ libraries.


API documentation
------------------

The documentation is located at https://yarl.aio-libs.org.


Why isn't boolean supported by the URL query API?
-------------------------------------------------

There is no standard for boolean representation of boolean values.

Some systems prefer ``true``/``false``, others like ``yes``/``no``, ``on``/``off``,
``Y``/``N``, ``1``/``0``, etc.

``yarl`` cannot make an unambiguous decision on how to serialize ``bool`` values because
it is specific to how the end-user's application is built and would be different for
different apps.  The library doesn't accept booleans in the API; a user should convert
bools into strings using own preferred translation protocol.


Comparison with other URL libraries
------------------------------------

* furl (https://pypi.python.org/pypi/furl)

  The library has rich functionality but the ``furl`` object is mutable.

  I'm afraid to pass this object into foreign code: who knows if the
  code will modify my url in a terrible way while I just want to send URL
  with handy helpers for accessing URL properties.

  ``furl`` has other non-obvious tricky things but the main objection
  is mutability.

* URLObject (https://pypi.python.org/pypi/URLObject)

  URLObject is immutable, that's pretty good.

  Every URL change generates a new URL object.

  But the library doesn't do any decode/encode transformations leaving the
  end user to cope with these gory details.


Source code
-----------

The project is hosted on GitHub_

Please file an issue on the `bug tracker
<https://github.com/aio-libs/yarl/issues>`_ if you have found a bug
or have some suggestion in order to improve the library.

Discussion list
---------------

*aio-libs* google group: https://groups.google.com/forum/#!forum/aio-libs

Feel free to post your questions and ideas here.


Authors and License
-------------------

The ``yarl`` package is written by Andrew Svetlov.

It's *Apache 2* licensed and freely available.


.. _GitHub: https://github.com/aio-libs/yarl

.. _multidict: https://github.com/aio-libs/multidict

.. _propcache: https://github.com/aio-libs/propcache

=========
Changelog
=========

..
    You should *NOT* be adding new change log entries to this file, this
    file is managed by towncrier. You *may* edit previous change logs to
    fix problems like typo corrections or such.
    To add a new change log entry, please see
    https://pip.pypa.io/en/latest/development/#adding-a-news-entry
    we named the news folder "changes".

    WARNING: Don't drop the next directive!

.. towncrier release notes start

1.20.1
======

*(2025-06-09)*


Bug fixes
---------

- Started raising a ``ValueError`` exception raised for corrupted
  IPv6 URL values.

  These fixes the issue where exception ``IndexError`` was
  leaking from the internal code because of not being handled and
  transformed into a user-facing error. The problem was happening
  under the following conditions: empty IPv6 URL, brackets in
  reverse order.

  -- by `@MaelPic <https://github.com/sponsors/MaelPic>`__.

  *Related issues and pull requests on GitHub:*
  `#1512 <https://github.com/aio-libs/yarl/issues/1512>`__.


Packaging updates and notes for downstreams
-------------------------------------------

- Updated to use Cython 3.1 universally across the build path -- by `@lysnikolaou <https://github.com/sponsors/lysnikolaou>`__.

  *Related issues and pull requests on GitHub:*
  `#1514 <https://github.com/aio-libs/yarl/issues/1514>`__.

- Made Cython line tracing opt-in via the ``with-cython-tracing`` build config setting -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  Previously, line tracing was enabled by default in ``pyproject.toml``, which caused build issues for some users and made wheels nearly twice as slow.
  Now line tracing is only enabled when explicitly requested via ``pip install . --config-setting=with-cython-tracing=true`` or by setting the ``YARL_CYTHON_TRACING`` environment variable.

  *Related issues and pull requests on GitHub:*
  `#1521 <https://github.com/aio-libs/yarl/issues/1521>`__.


----


1.20.0
======

*(2025-04-16)*


Features
--------

- Implemented support for the free-threaded build of CPython 3.13 -- by `@lysnikolaou <https://github.com/sponsors/lysnikolaou>`__.

  *Related issues and pull requests on GitHub:*
  `#1456 <https://github.com/aio-libs/yarl/issues/1456>`__.


Packaging updates and notes for downstreams
-------------------------------------------

- Started building wheels for the free-threaded build of CPython 3.13 -- by `@lysnikolaou <https://github.com/sponsors/lysnikolaou>`__.

  *Related issues and pull requests on GitHub:*
  `#1456 <https://github.com/aio-libs/yarl/issues/1456>`__.


----


1.19.0
======

*(2025-04-05)*


Bug fixes
---------

- Fixed entire name being re-encoded when using ``yarl.URL.with_suffix()`` -- by `@NTFSvolume <https://github.com/sponsors/NTFSvolume>`__.

  *Related issues and pull requests on GitHub:*
  `#1468 <https://github.com/aio-libs/yarl/issues/1468>`__.


Features
--------

- Started building armv7l wheels for manylinux -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1495 <https://github.com/aio-libs/yarl/issues/1495>`__.


Contributor-facing changes
--------------------------

- GitHub Actions CI/CD is now configured to manage caching pip-ecosystem
  dependencies using `re-actors/cache-python-deps`_ -- an action by
  `@webknjaz <https://github.com/sponsors/webknjaz>`__ that takes into account ABI stability and the exact
  version of Python runtime.

  .. _`re-actors/cache-python-deps`:
     https://github.com/marketplace/actions/cache-python-deps

  *Related issues and pull requests on GitHub:*
  `#1471 <https://github.com/aio-libs/yarl/issues/1471>`__.

- Increased minimum `propcache`_ version to 0.2.1 to fix failing tests -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  .. _`propcache`:
     https://github.com/aio-libs/propcache

  *Related issues and pull requests on GitHub:*
  `#1479 <https://github.com/aio-libs/yarl/issues/1479>`__.

- Added all hidden folders to pytest's ``norecursedirs`` to prevent it
  from trying to collect tests there -- by `@lysnikolaou <https://github.com/sponsors/lysnikolaou>`__.

  *Related issues and pull requests on GitHub:*
  `#1480 <https://github.com/aio-libs/yarl/issues/1480>`__.


Miscellaneous internal changes
------------------------------

- Improved accuracy of type annotations -- by `@Dreamsorcerer <https://github.com/sponsors/Dreamsorcerer>`__.

  *Related issues and pull requests on GitHub:*
  `#1484 <https://github.com/aio-libs/yarl/issues/1484>`__.

- Improved performance of parsing query strings -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1493 <https://github.com/aio-libs/yarl/issues/1493>`__, `#1497 <https://github.com/aio-libs/yarl/issues/1497>`__.

- Improved performance of the C unquoter -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1496 <https://github.com/aio-libs/yarl/issues/1496>`__, `#1498 <https://github.com/aio-libs/yarl/issues/1498>`__.


----


1.18.3
======

*(2024-12-01)*


Bug fixes
---------

- Fixed uppercase ASCII hosts being rejected by ``URL.build()()`` and ``yarl.URL.with_host()`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#954 <https://github.com/aio-libs/yarl/issues/954>`__, `#1442 <https://github.com/aio-libs/yarl/issues/1442>`__.


Miscellaneous internal changes
------------------------------

- Improved performances of multiple path properties on cache miss -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1443 <https://github.com/aio-libs/yarl/issues/1443>`__.


----


1.18.2
======

*(2024-11-29)*


No significant changes.


----


1.18.1
======

*(2024-11-29)*


Miscellaneous internal changes
------------------------------

- Improved cache performance when ``~yarl.URL`` objects are constructed from ``yarl.URL.build()`` with ``encoded=True`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1432 <https://github.com/aio-libs/yarl/issues/1432>`__.

- Improved cache performance for operations that produce a new ``~yarl.URL`` object -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1434 <https://github.com/aio-libs/yarl/issues/1434>`__, `#1436 <https://github.com/aio-libs/yarl/issues/1436>`__.


----


1.18.0
======

*(2024-11-21)*


Features
--------

- Added ``keep_query`` and ``keep_fragment`` flags in the ``yarl.URL.with_path()``, ``yarl.URL.with_name()`` and ``yarl.URL.with_suffix()`` methods, allowing users to optionally retain the query string and fragment in the resulting URL when replacing the path -- by `@paul-nameless <https://github.com/sponsors/paul-nameless>`__.

  *Related issues and pull requests on GitHub:*
  `#111 <https://github.com/aio-libs/yarl/issues/111>`__, `#1421 <https://github.com/aio-libs/yarl/issues/1421>`__.


Contributor-facing changes
--------------------------

- Started running downstream ``aiohttp`` tests in CI -- by `@Cycloctane <https://github.com/sponsors/Cycloctane>`__.

  *Related issues and pull requests on GitHub:*
  `#1415 <https://github.com/aio-libs/yarl/issues/1415>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of converting ``~yarl.URL`` to a string -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1422 <https://github.com/aio-libs/yarl/issues/1422>`__.


----


1.17.2
======

*(2024-11-17)*


Bug fixes
---------

- Stopped implicitly allowing the use of Cython pre-release versions when
  building the distribution package -- by `@ajsanchezsanz <https://github.com/sponsors/ajsanchezsanz>`__ and
  `@markgreene74 <https://github.com/sponsors/markgreene74>`__.

  *Related issues and pull requests on GitHub:*
  `#1411 <https://github.com/aio-libs/yarl/issues/1411>`__, `#1412 <https://github.com/aio-libs/yarl/issues/1412>`__.

- Fixed a bug causing ``~yarl.URL.port`` to return the default port when the given port was zero
  -- by `@gmacon <https://github.com/sponsors/gmacon>`__.

  *Related issues and pull requests on GitHub:*
  `#1413 <https://github.com/aio-libs/yarl/issues/1413>`__.


Features
--------

- Make error messages include details of incorrect type when ``port`` is not int in ``yarl.URL.build()``.
  -- by `@Cycloctane <https://github.com/sponsors/Cycloctane>`__.

  *Related issues and pull requests on GitHub:*
  `#1414 <https://github.com/aio-libs/yarl/issues/1414>`__.


Packaging updates and notes for downstreams
-------------------------------------------

- Stopped implicitly allowing the use of Cython pre-release versions when
  building the distribution package -- by `@ajsanchezsanz <https://github.com/sponsors/ajsanchezsanz>`__ and
  `@markgreene74 <https://github.com/sponsors/markgreene74>`__.

  *Related issues and pull requests on GitHub:*
  `#1411 <https://github.com/aio-libs/yarl/issues/1411>`__, `#1412 <https://github.com/aio-libs/yarl/issues/1412>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of the ``yarl.URL.joinpath()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1418 <https://github.com/aio-libs/yarl/issues/1418>`__.


----


1.17.1
======

*(2024-10-30)*


Miscellaneous internal changes
------------------------------

- Improved performance of many ``~yarl.URL`` methods -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1396 <https://github.com/aio-libs/yarl/issues/1396>`__, `#1397 <https://github.com/aio-libs/yarl/issues/1397>`__, `#1398 <https://github.com/aio-libs/yarl/issues/1398>`__.

- Improved performance of passing a `dict` or `str` to ``yarl.URL.extend_query()`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1401 <https://github.com/aio-libs/yarl/issues/1401>`__.


----


1.17.0
======

*(2024-10-28)*


Features
--------

- Added ``~yarl.URL.host_port_subcomponent`` which returns the ``3986#section-3.2.2`` host and ``3986#section-3.2.3`` port subcomponent -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1375 <https://github.com/aio-libs/yarl/issues/1375>`__.


----


1.16.0
======

*(2024-10-21)*


Bug fixes
---------

- Fixed blocking I/O to load Python code when creating a new ``~yarl.URL`` with non-ascii characters in the network location part -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1342 <https://github.com/aio-libs/yarl/issues/1342>`__.


Removals and backward incompatible breaking changes
---------------------------------------------------

- Migrated to using a single cache for encoding hosts -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  Passing ``ip_address_size`` and ``host_validate_size`` to ``yarl.cache_configure()`` is deprecated in favor of the new ``encode_host_size`` parameter and will be removed in a future release. For backwards compatibility, the old parameters affect the ``encode_host`` cache size.

  *Related issues and pull requests on GitHub:*
  `#1348 <https://github.com/aio-libs/yarl/issues/1348>`__, `#1357 <https://github.com/aio-libs/yarl/issues/1357>`__, `#1363 <https://github.com/aio-libs/yarl/issues/1363>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of constructing ``~yarl.URL`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1336 <https://github.com/aio-libs/yarl/issues/1336>`__.

- Improved performance of calling ``yarl.URL.build()`` and constructing unencoded ``~yarl.URL`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1345 <https://github.com/aio-libs/yarl/issues/1345>`__.

- Reworked the internal encoding cache to improve performance on cache hit -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1369 <https://github.com/aio-libs/yarl/issues/1369>`__.


----


1.15.5
======

*(2024-10-18)*


Miscellaneous internal changes
------------------------------

- Improved performance of the ``yarl.URL.joinpath()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1304 <https://github.com/aio-libs/yarl/issues/1304>`__.

- Improved performance of the ``yarl.URL.extend_query()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1305 <https://github.com/aio-libs/yarl/issues/1305>`__.

- Improved performance of the ``yarl.URL.origin()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1306 <https://github.com/aio-libs/yarl/issues/1306>`__.

- Improved performance of the ``yarl.URL.with_path()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1307 <https://github.com/aio-libs/yarl/issues/1307>`__.

- Improved performance of the ``yarl.URL.with_query()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1308 <https://github.com/aio-libs/yarl/issues/1308>`__, `#1328 <https://github.com/aio-libs/yarl/issues/1328>`__.

- Improved performance of the ``yarl.URL.update_query()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1309 <https://github.com/aio-libs/yarl/issues/1309>`__, `#1327 <https://github.com/aio-libs/yarl/issues/1327>`__.

- Improved performance of the ``yarl.URL.join()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1313 <https://github.com/aio-libs/yarl/issues/1313>`__.

- Improved performance of ``~yarl.URL`` equality checks -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1315 <https://github.com/aio-libs/yarl/issues/1315>`__.

- Improved performance of ``~yarl.URL`` methods that modify the network location -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1316 <https://github.com/aio-libs/yarl/issues/1316>`__.

- Improved performance of the ``yarl.URL.with_fragment()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1317 <https://github.com/aio-libs/yarl/issues/1317>`__.

- Improved performance of calculating the hash of ``~yarl.URL`` objects -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1318 <https://github.com/aio-libs/yarl/issues/1318>`__.

- Improved performance of the ``yarl.URL.relative()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1319 <https://github.com/aio-libs/yarl/issues/1319>`__.

- Improved performance of the ``yarl.URL.with_name()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1320 <https://github.com/aio-libs/yarl/issues/1320>`__.

- Improved performance of ``~yarl.URL.parent`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1321 <https://github.com/aio-libs/yarl/issues/1321>`__.

- Improved performance of the ``yarl.URL.with_scheme()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1322 <https://github.com/aio-libs/yarl/issues/1322>`__.


----


1.15.4
======

*(2024-10-16)*


Miscellaneous internal changes
------------------------------

- Improved performance of the quoter when all characters are safe -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1288 <https://github.com/aio-libs/yarl/issues/1288>`__.

- Improved performance of unquoting strings -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1292 <https://github.com/aio-libs/yarl/issues/1292>`__, `#1293 <https://github.com/aio-libs/yarl/issues/1293>`__.

- Improved performance of calling ``yarl.URL.build()`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1297 <https://github.com/aio-libs/yarl/issues/1297>`__.


----


1.15.3
======

*(2024-10-15)*


Bug fixes
---------

- Fixed ``yarl.URL.build()`` failing to validate paths must start with a ``/`` when passing ``authority`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  The validation only worked correctly when passing ``host``.

  *Related issues and pull requests on GitHub:*
  `#1265 <https://github.com/aio-libs/yarl/issues/1265>`__.


Removals and backward incompatible breaking changes
---------------------------------------------------

- Removed support for Python 3.8 as it has reached end of life -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1203 <https://github.com/aio-libs/yarl/issues/1203>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of constructing ``~yarl.URL`` when the net location is only the host -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1271 <https://github.com/aio-libs/yarl/issues/1271>`__.


----


1.15.2
======

*(2024-10-13)*


Miscellaneous internal changes
------------------------------

- Improved performance of converting ``~yarl.URL`` to a string -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1234 <https://github.com/aio-libs/yarl/issues/1234>`__.

- Improved performance of ``yarl.URL.joinpath()`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1248 <https://github.com/aio-libs/yarl/issues/1248>`__, `#1250 <https://github.com/aio-libs/yarl/issues/1250>`__.

- Improved performance of constructing query strings from ``~multidict.MultiDict`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1256 <https://github.com/aio-libs/yarl/issues/1256>`__.

- Improved performance of constructing query strings with ``int`` values -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1259 <https://github.com/aio-libs/yarl/issues/1259>`__.


----


1.15.1
======

*(2024-10-12)*


Miscellaneous internal changes
------------------------------

- Improved performance of calling ``yarl.URL.build()`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1222 <https://github.com/aio-libs/yarl/issues/1222>`__.

- Improved performance of all ``~yarl.URL`` methods that create new ``~yarl.URL`` objects -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1226 <https://github.com/aio-libs/yarl/issues/1226>`__.

- Improved performance of ``~yarl.URL`` methods that modify the network location -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1229 <https://github.com/aio-libs/yarl/issues/1229>`__.


----


1.15.0
======

*(2024-10-11)*


Bug fixes
---------

- Fixed validation with ``yarl.URL.with_scheme()`` when passed scheme is not lowercase -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1189 <https://github.com/aio-libs/yarl/issues/1189>`__.


Features
--------

- Started building ``armv7l`` wheels -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1204 <https://github.com/aio-libs/yarl/issues/1204>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of constructing unencoded ``~yarl.URL`` objects -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1188 <https://github.com/aio-libs/yarl/issues/1188>`__.

- Added a cache for parsing hosts to reduce overhead of encoding ``~yarl.URL`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1190 <https://github.com/aio-libs/yarl/issues/1190>`__.

- Improved performance of constructing query strings from ``~collections.abc.Mapping`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1193 <https://github.com/aio-libs/yarl/issues/1193>`__.

- Improved performance of converting ``~yarl.URL`` objects to strings -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1198 <https://github.com/aio-libs/yarl/issues/1198>`__.


----


1.14.0
======

*(2024-10-08)*


Packaging updates and notes for downstreams
-------------------------------------------

- Switched to using the ``propcache`` package for property caching
  -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  The ``propcache`` package is derived from the property caching
  code in ``yarl`` and has been broken out to avoid maintaining it for multiple
  projects.

  *Related issues and pull requests on GitHub:*
  `#1169 <https://github.com/aio-libs/yarl/issues/1169>`__.


Contributor-facing changes
--------------------------

- Started testing with Hypothesis -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__ and `@bdraco <https://github.com/sponsors/bdraco>`__.

  Special thanks to `@Zac-HD <https://github.com/sponsors/Zac-HD>`__ for helping us get started with this framework.

  *Related issues and pull requests on GitHub:*
  `#860 <https://github.com/aio-libs/yarl/issues/860>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of ``yarl.URL.is_default_port()`` when no explicit port is set -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1168 <https://github.com/aio-libs/yarl/issues/1168>`__.

- Improved performance of converting ``~yarl.URL`` to a string when no explicit port is set -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1170 <https://github.com/aio-libs/yarl/issues/1170>`__.

- Improved performance of the ``yarl.URL.origin()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1175 <https://github.com/aio-libs/yarl/issues/1175>`__.

- Improved performance of encoding hosts -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1176 <https://github.com/aio-libs/yarl/issues/1176>`__.


----


1.13.1
======

*(2024-09-27)*


Miscellaneous internal changes
------------------------------

- Improved performance of calling ``yarl.URL.build()`` with ``authority`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1163 <https://github.com/aio-libs/yarl/issues/1163>`__.


----


1.13.0
======

*(2024-09-26)*


Bug fixes
---------

- Started rejecting ASCII hostnames with invalid characters. For host strings that
  look like authority strings, the exception message includes advice on what to do
  instead -- by `@mjpieters <https://github.com/sponsors/mjpieters>`__.

  *Related issues and pull requests on GitHub:*
  `#880 <https://github.com/aio-libs/yarl/issues/880>`__, `#954 <https://github.com/aio-libs/yarl/issues/954>`__.

- Fixed IPv6 addresses missing brackets when the ``~yarl.URL`` was converted to a string -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1157 <https://github.com/aio-libs/yarl/issues/1157>`__, `#1158 <https://github.com/aio-libs/yarl/issues/1158>`__.


Features
--------

- Added ``~yarl.URL.host_subcomponent`` which returns the ``3986#section-3.2.2`` host subcomponent -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  The only current practical difference between ``~yarl.URL.raw_host`` and ``~yarl.URL.host_subcomponent`` is that IPv6 addresses are returned bracketed.

  *Related issues and pull requests on GitHub:*
  `#1159 <https://github.com/aio-libs/yarl/issues/1159>`__.


----


1.12.1
======

*(2024-09-23)*


No significant changes.


----


1.12.0
======

*(2024-09-23)*


Features
--------

- Added ``~yarl.URL.path_safe`` to be able to fetch the path without ``%2F`` and ``%25`` decoded -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1150 <https://github.com/aio-libs/yarl/issues/1150>`__.


Removals and backward incompatible breaking changes
---------------------------------------------------

- Restore decoding ``%2F`` (``/``) in ``URL.path`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  This change restored the behavior before `#1057 <https://github.com/aio-libs/yarl/issues/1057>`__.

  *Related issues and pull requests on GitHub:*
  `#1151 <https://github.com/aio-libs/yarl/issues/1151>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of processing paths -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1143 <https://github.com/aio-libs/yarl/issues/1143>`__.


----


1.11.1
======

*(2024-09-09)*


Bug fixes
---------

- Allowed scheme replacement for relative URLs if the scheme does not require a host -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#280 <https://github.com/aio-libs/yarl/issues/280>`__, `#1138 <https://github.com/aio-libs/yarl/issues/1138>`__.

- Allowed empty host for URL schemes other than the special schemes listed in the WHATWG URL spec -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1136 <https://github.com/aio-libs/yarl/issues/1136>`__.


Features
--------

- Loosened restriction on integers as query string values to allow classes that implement ``__int__`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1139 <https://github.com/aio-libs/yarl/issues/1139>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of normalizing paths -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1137 <https://github.com/aio-libs/yarl/issues/1137>`__.


----


1.11.0
======

*(2024-09-08)*


Features
--------

- Added ``URL.extend_query()()`` method, which can be used to extend parameters without replacing same named keys -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  This method was primarily added to replace the inefficient hand rolled method currently used in ``aiohttp``.

  *Related issues and pull requests on GitHub:*
  `#1128 <https://github.com/aio-libs/yarl/issues/1128>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of the Cython ``cached_property`` implementation -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1122 <https://github.com/aio-libs/yarl/issues/1122>`__.

- Simplified computing ports by removing unnecessary code -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1123 <https://github.com/aio-libs/yarl/issues/1123>`__.

- Improved performance of encoding non IPv6 hosts -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1125 <https://github.com/aio-libs/yarl/issues/1125>`__.

- Improved performance of ``URL.build()()`` when the path, query string, or fragment is an empty string -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1126 <https://github.com/aio-libs/yarl/issues/1126>`__.

- Improved performance of the ``URL.update_query()()`` method -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1130 <https://github.com/aio-libs/yarl/issues/1130>`__.

- Improved performance of processing query string changes when arguments are ``str`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1131 <https://github.com/aio-libs/yarl/issues/1131>`__.


----


1.10.0
======

*(2024-09-06)*


Bug fixes
---------

- Fixed joining a path when the existing path was empty -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  A regression in ``URL.join()()`` was introduced in `#1082 <https://github.com/aio-libs/yarl/issues/1082>`__.

  *Related issues and pull requests on GitHub:*
  `#1118 <https://github.com/aio-libs/yarl/issues/1118>`__.


Features
--------

- Added ``URL.without_query_params()()`` method, to drop some parameters from query string -- by `@hongquan <https://github.com/sponsors/hongquan>`__.

  *Related issues and pull requests on GitHub:*
  `#774 <https://github.com/aio-libs/yarl/issues/774>`__, `#898 <https://github.com/aio-libs/yarl/issues/898>`__, `#1010 <https://github.com/aio-libs/yarl/issues/1010>`__.

- The previously protected types ``_SimpleQuery``, ``_QueryVariable``, and ``_Query`` are now available for use externally as ``SimpleQuery``, ``QueryVariable``, and ``Query`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1050 <https://github.com/aio-libs/yarl/issues/1050>`__, `#1113 <https://github.com/aio-libs/yarl/issues/1113>`__.


Contributor-facing changes
--------------------------

- Replaced all ``~typing.Optional`` with ``~typing.Union`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1095 <https://github.com/aio-libs/yarl/issues/1095>`__.


Miscellaneous internal changes
------------------------------

- Significantly improved performance of parsing the network location -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1112 <https://github.com/aio-libs/yarl/issues/1112>`__.

- Added internal types to the cache to prevent future refactoring errors -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1117 <https://github.com/aio-libs/yarl/issues/1117>`__.


----


1.9.11
======

*(2024-09-04)*


Bug fixes
---------

- Fixed a ``TypeError`` with ``MultiDictProxy`` and Python 3.8 -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1084 <https://github.com/aio-libs/yarl/issues/1084>`__, `#1105 <https://github.com/aio-libs/yarl/issues/1105>`__, `#1107 <https://github.com/aio-libs/yarl/issues/1107>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of encoding hosts -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  Previously, the library would unconditionally try to parse a host as an IP Address. The library now avoids trying to parse a host as an IP Address if the string is not in one of the formats described in ``3986#section-3.2.2``.

  *Related issues and pull requests on GitHub:*
  `#1104 <https://github.com/aio-libs/yarl/issues/1104>`__.


----


1.9.10
======

*(2024-09-04)*


Bug fixes
---------

- ``URL.join()()`` has been changed to match
  ``3986`` and align with
  ``/ operation()`` and ``URL.joinpath()()``
  when joining URLs with empty segments.
  Previously ``urllib.parse.urljoin`` was used,
  which has known issues with empty segments
  (`python/cpython#84774 <https://github.com/python/cpython/issues/84774>`_).

  Due to the semantics of ``URL.join()()``, joining an
  URL with scheme requires making it relative, prefixing with ``./``.

  .. code-block:: pycon

     >>> URL("https://web.archive.org/web/").join(URL("./https://github.com/aio-libs/yarl"))
     URL('https://web.archive.org/web/https://github.com/aio-libs/yarl')


  Empty segments are honored in the base as well as the joined part.

  .. code-block:: pycon

     >>> URL("https://web.archive.org/web/https://").join(URL("github.com/aio-libs/yarl"))
     URL('https://web.archive.org/web/https://github.com/aio-libs/yarl')



  -- by `@commonism <https://github.com/sponsors/commonism>`__

  This change initially appeared in 1.9.5 but was reverted in 1.9.6 to resolve a problem with query string handling.

  *Related issues and pull requests on GitHub:*
  `#1039 <https://github.com/aio-libs/yarl/issues/1039>`__, `#1082 <https://github.com/aio-libs/yarl/issues/1082>`__.


Features
--------

- Added ``~yarl.URL.absolute`` which is now preferred over ``URL.is_absolute()`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1100 <https://github.com/aio-libs/yarl/issues/1100>`__.


----


1.9.9
=====

*(2024-09-04)*


Bug fixes
---------

- Added missing type on ``~yarl.URL.port`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1097 <https://github.com/aio-libs/yarl/issues/1097>`__.


----


1.9.8
=====

*(2024-09-03)*


Features
--------

- Covered the ``~yarl.URL`` object with types -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1084 <https://github.com/aio-libs/yarl/issues/1084>`__.

- Cache parsing of IP Addresses when encoding hosts -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1086 <https://github.com/aio-libs/yarl/issues/1086>`__.


Contributor-facing changes
--------------------------

- Covered the ``~yarl.URL`` object with types -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1084 <https://github.com/aio-libs/yarl/issues/1084>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of handling ports -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  *Related issues and pull requests on GitHub:*
  `#1081 <https://github.com/aio-libs/yarl/issues/1081>`__.


----


1.9.7
=====

*(2024-09-01)*


Removals and backward incompatible breaking changes
---------------------------------------------------

- Removed support ``3986#section-3.2.3`` port normalization when the scheme is not one of ``http``, ``https``, ``wss``, or ``ws`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  Support for port normalization was recently added in `#1033 <https://github.com/aio-libs/yarl/issues/1033>`__ and contained code that would do blocking I/O if the scheme was not one of the four listed above. The code has been removed because this library is intended to be safe for usage with ``asyncio``.

  *Related issues and pull requests on GitHub:*
  `#1076 <https://github.com/aio-libs/yarl/issues/1076>`__.


Miscellaneous internal changes
------------------------------

- Improved performance of property caching -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  The ``reify`` implementation from ``aiohttp`` was adapted to replace the internal ``cached_property`` implementation.

  *Related issues and pull requests on GitHub:*
  `#1070 <https://github.com/aio-libs/yarl/issues/1070>`__.


----


1.9.6
=====

*(2024-08-30)*


Bug fixes
---------

- Reverted ``3986`` compatible ``URL.join()()`` honoring empty segments which was introduced in `#1039 <https://github.com/aio-libs/yarl/issues/1039>`__.

  This change introduced a regression handling query string parameters with joined URLs. The change was reverted to maintain compatibility with the previous behavior.

  *Related issues and pull requests on GitHub:*
  `#1067 <https://github.com/aio-libs/yarl/issues/1067>`__.


----


1.9.5
=====

*(2024-08-30)*


Bug fixes
---------

- Joining URLs with empty segments has been changed
  to match ``3986``.

  Previously empty segments would be removed from path,
  breaking use-cases such as

  .. code-block:: python

     URL("https://web.archive.org/web/") / "https://github.com/"

  Now ``/ operation()`` and ``URL.joinpath()()``
  keep empty segments, but do not introduce new empty segments.
  e.g.

  .. code-block:: python

     URL("https://example.org/") / ""

  does not introduce an empty segment.

  -- by `@commonism <https://github.com/sponsors/commonism>`__ and `@youtux <https://github.com/sponsors/youtux>`__

  *Related issues and pull requests on GitHub:*
  `#1026 <https://github.com/aio-libs/yarl/issues/1026>`__.

- The default protocol ports of well-known URI schemes are now taken into account
  during the normalization of the URL string representation in accordance with
  ``3986#section-3.2.3``.

  Specified ports are removed from the ``str`` representation of a ``~yarl.URL``
  if the port matches the scheme's default port -- by `@commonism <https://github.com/sponsors/commonism>`__.

  *Related issues and pull requests on GitHub:*
  `#1033 <https://github.com/aio-libs/yarl/issues/1033>`__.

- ``URL.join()()`` has been changed to match
  ``3986`` and align with
  ``/ operation()`` and ``URL.joinpath()()``
  when joining URLs with empty segments.
  Previously ``urllib.parse.urljoin`` was used,
  which has known issues with empty segments
  (`python/cpython#84774 <https://github.com/python/cpython/issues/84774>`_).

  Due to the semantics of ``URL.join()()``, joining an
  URL with scheme requires making it relative, prefixing with ``./``.

  .. code-block:: pycon

     >>> URL("https://web.archive.org/web/").join(URL("./https://github.com/aio-libs/yarl"))
     URL('https://web.archive.org/web/https://github.com/aio-libs/yarl')


  Empty segments are honored in the base as well as the joined part.

  .. code-block:: pycon

     >>> URL("https://web.archive.org/web/https://").join(URL("github.com/aio-libs/yarl"))
     URL('https://web.archive.org/web/https://github.com/aio-libs/yarl')



  -- by `@commonism <https://github.com/sponsors/commonism>`__

  *Related issues and pull requests on GitHub:*
  `#1039 <https://github.com/aio-libs/yarl/issues/1039>`__.


Removals and backward incompatible breaking changes
---------------------------------------------------

- Stopped decoding ``%2F`` (``/``) in ``URL.path``, as this could lead to code incorrectly treating it as a path separator
  -- by `@Dreamsorcerer <https://github.com/sponsors/Dreamsorcerer>`__.

  *Related issues and pull requests on GitHub:*
  `#1057 <https://github.com/aio-libs/yarl/issues/1057>`__.

- Dropped support for Python 3.7 -- by `@Dreamsorcerer <https://github.com/sponsors/Dreamsorcerer>`__.

  *Related issues and pull requests on GitHub:*
  `#1016 <https://github.com/aio-libs/yarl/issues/1016>`__.


Improved documentation
----------------------

- On the ``Contributing docs`` page,
  a link to the ``Towncrier philosophy`` has been fixed.

  *Related issues and pull requests on GitHub:*
  `#981 <https://github.com/aio-libs/yarl/issues/981>`__.

- The pre-existing ``/ magic method()``
  has been documented in the API reference -- by `@commonism <https://github.com/sponsors/commonism>`__.

  *Related issues and pull requests on GitHub:*
  `#1026 <https://github.com/aio-libs/yarl/issues/1026>`__.


Packaging updates and notes for downstreams
-------------------------------------------

- A flaw in the logic for copying the project directory into a
  temporary folder that led to infinite recursion when ``TMPDIR``
  was set to a project subdirectory path. This was happening in Fedora
  and its downstream due to the use of `pyproject-rpm-macros
  <https://src.fedoraproject.org/rpms/pyproject-rpm-macros>`__. It was
  only reproducible with ``pip wheel`` and was not affecting the
  ``pyproject-build`` users.

  -- by `@hroncok <https://github.com/sponsors/hroncok>`__ and `@webknjaz <https://github.com/sponsors/webknjaz>`__

  *Related issues and pull requests on GitHub:*
  `#992 <https://github.com/aio-libs/yarl/issues/992>`__, `#1014 <https://github.com/aio-libs/yarl/issues/1014>`__.

- Support Python 3.13 and publish non-free-threaded wheels

  *Related issues and pull requests on GitHub:*
  `#1054 <https://github.com/aio-libs/yarl/issues/1054>`__.


Contributor-facing changes
--------------------------

- The CI/CD setup has been updated to test ``arm64`` wheels
  under macOS 14, except for Python 3.7 that is unsupported
  in that environment -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  *Related issues and pull requests on GitHub:*
  `#1015 <https://github.com/aio-libs/yarl/issues/1015>`__.

- Removed unused type ignores and casts -- by `@hauntsaninja <https://github.com/sponsors/hauntsaninja>`__.

  *Related issues and pull requests on GitHub:*
  `#1031 <https://github.com/aio-libs/yarl/issues/1031>`__.


Miscellaneous internal changes
------------------------------

- ``port``, ``scheme``, and ``raw_host`` are now ``cached_property`` -- by `@bdraco <https://github.com/sponsors/bdraco>`__.

  ``aiohttp`` accesses these properties quite often, which cause ``urllib`` to build the ``_hostinfo`` property every time. ``port``, ``scheme``, and ``raw_host`` are now cached properties, which will improve performance.

  *Related issues and pull requests on GitHub:*
  `#1044 <https://github.com/aio-libs/yarl/issues/1044>`__, `#1058 <https://github.com/aio-libs/yarl/issues/1058>`__.


----


1.9.4 (2023-12-06)
==================

Bug fixes
---------

- Started raising ``TypeError`` when a string value is passed into
  ``yarl.URL.build()`` as the ``port`` argument  -- by `@commonism <https://github.com/sponsors/commonism>`__.

  Previously the empty string as port would create malformed URLs when rendered as string representations. (`#883 <https://github.com/aio-libs/yarl/issues/883>`__)


Packaging updates and notes for downstreams
-------------------------------------------

- The leading ``--`` has been dropped from the `PEP 517 <https://peps.python.org/pep-517>`__ in-tree build
  backend config setting names. ``--pure-python`` is now just ``pure-python``
  -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  The usage now looks as follows:

  .. code-block:: console

      $ python -m build \
          --config-setting=pure-python=true \
          --config-setting=with-cython-tracing=true

  (`#963 <https://github.com/aio-libs/yarl/issues/963>`__)


Contributor-facing changes
--------------------------

- A step-by-step ``Release Guide`` guide has
  been added, describing how to release *yarl* -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  This is primarily targeting maintainers. (`#960 <https://github.com/aio-libs/yarl/issues/960>`__)
- Coverage collection has been implemented for the Cython modules
  -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  It will also be reported to Codecov from any non-release CI jobs.

  To measure coverage in a development environment, *yarl* can be
  installed in editable mode:

  .. code-block:: console

      $ python -Im pip install -e .

  Editable install produces C-files required for the Cython coverage
  plugin to map the measurements back to the PYX-files.

  `#961 <https://github.com/aio-libs/yarl/issues/961>`__

- It is now possible to request line tracing in Cython builds using the
  ``with-cython-tracing`` `PEP 517 <https://peps.python.org/pep-517>`__ config setting
  -- `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  This can be used in CI and development environment to measure coverage
  on Cython modules, but is not normally useful to the end-users or
  downstream packagers.

  Here's a usage example:

  .. code-block:: console

      $ python -Im pip install . --config-settings=with-cython-tracing=true

  For editable installs, this setting is on by default. Otherwise, it's
  off unless requested explicitly.

  The following produces C-files required for the Cython coverage
  plugin to map the measurements back to the PYX-files:

  .. code-block:: console

      $ python -Im pip install -e .

  Alternatively, the ``YARL_CYTHON_TRACING=1`` environment variable
  can be set to do the same as the `PEP 517 <https://peps.python.org/pep-517>`__ config setting.

  `#962 <https://github.com/aio-libs/yarl/issues/962>`__


1.9.3 (2023-11-20)
==================

Bug fixes
---------

- Stopped dropping trailing slashes in ``yarl.URL.joinpath()`` -- by `@gmacon <https://github.com/sponsors/gmacon>`__. (`#862 <https://github.com/aio-libs/yarl/issues/862>`__, `#866 <https://github.com/aio-libs/yarl/issues/866>`__)
- Started accepting string subclasses in ``yarl.URL.__truediv__()`` operations (``URL / segment``) -- by `@mjpieters <https://github.com/sponsors/mjpieters>`__. (`#871 <https://github.com/aio-libs/yarl/issues/871>`__, `#884 <https://github.com/aio-libs/yarl/issues/884>`__)
- Fixed the human representation of URLs with square brackets in usernames and passwords -- by `@mjpieters <https://github.com/sponsors/mjpieters>`__. (`#876 <https://github.com/aio-libs/yarl/issues/876>`__, `#882 <https://github.com/aio-libs/yarl/issues/882>`__)
- Updated type hints to include ``URL.missing_port()``, ``URL.__bytes__()``
  and the ``encoding`` argument to ``yarl.URL.joinpath()``
  -- by `@mjpieters <https://github.com/sponsors/mjpieters>`__. (`#891 <https://github.com/aio-libs/yarl/issues/891>`__)


Packaging updates and notes for downstreams
-------------------------------------------

- Integrated Cython 3 to enable building *yarl* under Python 3.12 -- by `@mjpieters <https://github.com/sponsors/mjpieters>`__. (`#829 <https://github.com/aio-libs/yarl/issues/829>`__, `#881 <https://github.com/aio-libs/yarl/issues/881>`__)
- Declared modern ``setuptools.build_meta`` as the `PEP 517 <https://peps.python.org/pep-517>`__ build
  backend in ``pyproject.toml`` explicitly -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__. (`#886 <https://github.com/aio-libs/yarl/issues/886>`__)
- Converted most of the packaging setup into a declarative ``setup.cfg``
  config -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__. (`#890 <https://github.com/aio-libs/yarl/issues/890>`__)
- The packaging is replaced from an old-fashioned ``setup.py`` to an
  in-tree `PEP 517 <https://peps.python.org/pep-517>`__ build backend -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  Whenever the end-users or downstream packagers need to build ``yarl`` from
  source (a Git checkout or an sdist), they may pass a ``config_settings``
  flag ``--pure-python``. If this flag is not set, a C-extension will be built
  and included into the distribution.

  Here is how this can be done with ``pip``:

  .. code-block:: console

      $ python -m pip install . --config-settings=--pure-python=false

  This will also work with ``-e | --editable``.

  The same can be achieved via ``pypa/build``:

  .. code-block:: console

      $ python -m build --config-setting=--pure-python=false

  Adding ``-w | --wheel`` can force ``pypa/build`` produce a wheel from source
  directly, as opposed to building an ``sdist`` and then building from it. (`#893 <https://github.com/aio-libs/yarl/issues/893>`__)

  .. attention::

     v1.9.3 was the only version using the ``--pure-python`` setting name.
     Later versions dropped the ``--`` prefix, making it just ``pure-python``.

- Declared Python 3.12 supported officially in the distribution package metadata
  -- by `@edgarrmondragon <https://github.com/sponsors/edgarrmondragon>`__. (`#942 <https://github.com/aio-libs/yarl/issues/942>`__)


Contributor-facing changes
--------------------------

- A regression test for no-host URLs was added per `#821 <https://github.com/aio-libs/yarl/issues/821>`__
  and ``3986`` -- by `@kenballus <https://github.com/sponsors/kenballus>`__. (`#821 <https://github.com/aio-libs/yarl/issues/821>`__, `#822 <https://github.com/aio-libs/yarl/issues/822>`__)
- Started testing *yarl* against Python 3.12 in CI -- by `@mjpieters <https://github.com/sponsors/mjpieters>`__. (`#881 <https://github.com/aio-libs/yarl/issues/881>`__)
- All Python 3.12 jobs are now marked as required to pass in CI
  -- by `@edgarrmondragon <https://github.com/sponsors/edgarrmondragon>`__. (`#942 <https://github.com/aio-libs/yarl/issues/942>`__)
- MyST is now integrated in Sphinx -- by `@webknjaz <https://github.com/sponsors/webknjaz>`__.

  This allows the contributors to author new documents in Markdown
  when they have difficulties with going straight RST. (`#953 <https://github.com/aio-libs/yarl/issues/953>`__)


1.9.2 (2023-04-25)
==================

Bugfixes
--------

- Fix regression with ``yarl.URL.__truediv__()`` and absolute URLs with empty paths causing the raw path to lack the leading ``/``.
  (`#854 <https://github.com/aio-libs/yarl/issues/854>`_)


1.9.1 (2023-04-21)
==================

Bugfixes
--------

- Marked tests that fail on older Python patch releases (< 3.7.10, < 3.8.8 and < 3.9.2) as expected to fail due to missing a security fix for CVE-2021-23336. (`#850 <https://github.com/aio-libs/yarl/issues/850>`_)


1.9.0 (2023-04-19)
==================

This release was never published to PyPI, due to issues with the build process.

Features
--------

- Added ``URL.joinpath(*elements)``, to create a new URL appending multiple path elements. (`#704 <https://github.com/aio-libs/yarl/issues/704>`_)
- Made ``URL.__truediv__()()`` return ``NotImplemented`` if called with an
  unsupported type — by `@michaeljpeters <https://github.com/sponsors/michaeljpeters>`__.
  (`#832 <https://github.com/aio-libs/yarl/issues/832>`_)


Bugfixes
--------

- Path normalization for absolute URLs no longer raises a ValueError exception
  when ``..`` segments would otherwise go beyond the URL path root.
  (`#536 <https://github.com/aio-libs/yarl/issues/536>`_)
- Fixed an issue with update_query() not getting rid of the query when argument is None. (`#792 <https://github.com/aio-libs/yarl/issues/792>`_)
- Added some input restrictions on with_port() function to prevent invalid boolean inputs or out of valid port inputs; handled incorrect 0 port representation. (`#793 <https://github.com/aio-libs/yarl/issues/793>`_)
- Made ``yarl.URL.build()`` raise a ``TypeError`` if the ``host`` argument is ``None`` — by `@paulpapacz <https://github.com/sponsors/paulpapacz>`__. (`#808 <https://github.com/aio-libs/yarl/issues/808>`_)
- Fixed an issue with ``update_query()`` getting rid of the query when the argument
  is empty but not ``None``. (`#845 <https://github.com/aio-libs/yarl/issues/845>`_)


Misc
----

- `#220 <https://github.com/aio-libs/yarl/issues/220>`_


1.8.2 (2022-12-03)
==================

This is the first release that started shipping wheels for Python 3.11.


1.8.1 (2022-08-01)
==================

Misc
----

- `#694 <https://github.com/aio-libs/yarl/issues/694>`_, `#699 <https://github.com/aio-libs/yarl/issues/699>`_, `#700 <https://github.com/aio-libs/yarl/issues/700>`_, `#701 <https://github.com/aio-libs/yarl/issues/701>`_, `#702 <https://github.com/aio-libs/yarl/issues/702>`_, `#703 <https://github.com/aio-libs/yarl/issues/703>`_, `#739 <https://github.com/aio-libs/yarl/issues/739>`_


1.8.0 (2022-08-01)
==================

Features
--------

- Added ``URL.raw_suffix``, ``URL.suffix``, ``URL.raw_suffixes``, ``URL.suffixes``, ``URL.with_suffix``. (`#613 <https://github.com/aio-libs/yarl/issues/613>`_)


Improved Documentation
----------------------

- Fixed broken internal references to ``yarl.URL.human_repr()``.
  (`#665 <https://github.com/aio-libs/yarl/issues/665>`_)
- Fixed broken external references to ``multidict:index`` docs. (`#665 <https://github.com/aio-libs/yarl/issues/665>`_)


Deprecations and Removals
-------------------------

- Dropped Python 3.6 support. (`#672 <https://github.com/aio-libs/yarl/issues/672>`_)


Misc
----

- `#646 <https://github.com/aio-libs/yarl/issues/646>`_, `#699 <https://github.com/aio-libs/yarl/issues/699>`_, `#701 <https://github.com/aio-libs/yarl/issues/701>`_


1.7.2 (2021-11-01)
==================

Bugfixes
--------

- Changed call in ``with_port()`` to stop reencoding parts of the URL that were already encoded. (`#623 <https://github.com/aio-libs/yarl/issues/623>`_)


1.7.1 (2021-10-07)
==================

Bugfixes
--------

- Fix 1.7.0 build error

1.7.0 (2021-10-06)
==================

Features
--------

- Add ``__bytes__()`` magic method so that ``bytes(url)`` will work and use optimal ASCII encoding.
  (`#582 <https://github.com/aio-libs/yarl/issues/582>`_)
- Started shipping platform-specific arm64 wheels for Apple Silicon. (`#622 <https://github.com/aio-libs/yarl/issues/622>`_)
- Started shipping platform-specific wheels with the ``musl`` tag targeting typical Alpine Linux runtimes. (`#622 <https://github.com/aio-libs/yarl/issues/622>`_)
- Added support for Python 3.10. (`#622 <https://github.com/aio-libs/yarl/issues/622>`_)


1.6.3 (2020-11-14)
==================

Bugfixes
--------

- No longer loose characters when decoding incorrect percent-sequences (like ``%e2%82%f8``). All non-decodable percent-sequences are now preserved.
  `#517 <https://github.com/aio-libs/yarl/issues/517>`_
- Provide x86 Windows wheels.
  `#535 <https://github.com/aio-libs/yarl/issues/535>`_


----


1.6.2 (2020-10-12)
==================


Bugfixes
--------

- Provide generated ``.c`` files in TarBall distribution.
  `#530  <https://github.com/aio-libs/multidict/issues/530>`_

1.6.1 (2020-10-12)
==================

Features
--------

- Provide wheels for ``aarch64``, ``i686``, ``ppc64le``, ``s390x`` architectures on
  Linux as well as ``x86_64``.
  `#507  <https://github.com/aio-libs/yarl/issues/507>`_
- Provide wheels for Python 3.9.
  `#526 <https://github.com/aio-libs/yarl/issues/526>`_

Bugfixes
--------

- ``human_repr()`` now always produces valid representation equivalent to the original URL (if the original URL is valid).
  `#511 <https://github.com/aio-libs/yarl/issues/511>`_
- Fixed  requoting a single percent followed by a percent-encoded character in the Cython implementation.
  `#514 <https://github.com/aio-libs/yarl/issues/514>`_
- Fix ValueError when decoding ``%`` which is not followed by two hexadecimal digits.
  `#516 <https://github.com/aio-libs/yarl/issues/516>`_
- Fix decoding ``%`` followed by a space and hexadecimal digit.
  `#520 <https://github.com/aio-libs/yarl/issues/520>`_
- Fix annotation of ``with_query()``/``update_query()`` methods for ``key=[val1, val2]`` case.
  `#528 <https://github.com/aio-libs/yarl/issues/528>`_

Removal
-------

- Drop Python 3.5 support; Python 3.6 is the minimal supported Python version.


----


1.6.0 (2020-09-23)
==================

Features
--------

- Allow for int and float subclasses in query, while still denying bool.
  `#492 <https://github.com/aio-libs/yarl/issues/492>`_


Bugfixes
--------

- Do not requote arguments in ``URL.build()``, ``with_xxx()`` and in ``/`` operator.
  `#502 <https://github.com/aio-libs/yarl/issues/502>`_
- Keep IPv6 brackets in ``origin()``.
  `#504 <https://github.com/aio-libs/yarl/issues/504>`_


----


1.5.1 (2020-08-01)
==================

Bugfixes
--------

- Fix including relocated internal ``yarl._quoting_c`` C-extension into published PyPI dists.
  `#485 <https://github.com/aio-libs/yarl/issues/485>`_


Misc
----

- `#484 <https://github.com/aio-libs/yarl/issues/484>`_


----


1.5.0 (2020-07-26)
==================

Features
--------

- Convert host to lowercase on URL building.
  `#386 <https://github.com/aio-libs/yarl/issues/386>`_
- Allow using ``mod`` operator (``%``) for updating query string (an alias for ``update_query()`` method).
  `#435 <https://github.com/aio-libs/yarl/issues/435>`_
- Allow use of sequences such as ``list`` and ``tuple`` in the values
  of a mapping such as ``dict`` to represent that a key has many values::

      url = URL("http://example.com")
      assert url.with_query({"a": [1, 2]}) == URL("http://example.com/?a=1&a=2")

  `#443 <https://github.com/aio-libs/yarl/issues/443>`_
- Support ``URL.build()`` with scheme and path (creates a relative URL).
  `#464 <https://github.com/aio-libs/yarl/issues/464>`_
- Cache slow IDNA encode/decode calls.
  `#476 <https://github.com/aio-libs/yarl/issues/476>`_
- Add ``@final`` / ``Final`` type hints
  `#477 <https://github.com/aio-libs/yarl/issues/477>`_
- Support URL authority/raw_authority properties and authority argument of ``URL.build()`` method.
  `#478 <https://github.com/aio-libs/yarl/issues/478>`_
- Hide the library implementation details, make the exposed public list very clean.
  `#483 <https://github.com/aio-libs/yarl/issues/483>`_


Bugfixes
--------

- Fix tests with newer Python (3.7.6, 3.8.1 and 3.9.0+).
  `#409 <https://github.com/aio-libs/yarl/issues/409>`_
- Fix a bug where query component, passed in a form of mapping or sequence, is unquoted in unexpected way.
  `#426 <https://github.com/aio-libs/yarl/issues/426>`_
- Hide ``Query`` and ``QueryVariable`` type aliases in ``__init__.pyi``, now they are prefixed with underscore.
  `#431 <https://github.com/aio-libs/yarl/issues/431>`_
- Keep IPv6 brackets after updating port/user/password.
  `#451 <https://github.com/aio-libs/yarl/issues/451>`_


----


1.4.2 (2019-12-05)
==================

Features
--------

- Workaround for missing ``str.isascii()`` in Python 3.6
  `#389 <https://github.com/aio-libs/yarl/issues/389>`_


----


1.4.1 (2019-11-29)
==================

* Fix regression, make the library work on Python 3.5 and 3.6 again.

1.4.0 (2019-11-29)
==================

* Distinguish an empty password in URL from a password not provided at all (#262)

* Fixed annotations for optional parameters of ``URL.build`` (#309)

* Use None as default value of ``user`` parameter of ``URL.build`` (#309)

* Enforce building C Accelerated modules when installing from source tarball, use
  ``YARL_NO_EXTENSIONS`` environment variable for falling back to (slower) Pure Python
  implementation (#329)

* Drop Python 3.5 support

* Fix quoting of plus in path by pure python version (#339)

* Don't create a new URL if fragment is unchanged (#292)

* Included in error message the path that produces starting slash forbidden error (#376)

* Skip slow IDNA encoding for ASCII-only strings (#387)


1.3.0 (2018-12-11)
==================

* Fix annotations for ``query`` parameter (#207)

* An incoming query sequence can have int variables (the same as for
  Mapping type) (#208)

* Add ``URL.explicit_port`` property (#218)

* Give a friendlier error when port can't be converted to int (#168)

* ``bool(URL())`` now returns ``False`` (#272)

1.2.6 (2018-06-14)
==================

* Drop Python 3.4 trove classifier (#205)

1.2.5 (2018-05-23)
==================

* Fix annotations for ``build`` (#199)

1.2.4 (2018-05-08)
==================

* Fix annotations for ``cached_property`` (#195)

1.2.3 (2018-05-03)
==================

* Accept ``str`` subclasses in ``URL`` constructor (#190)

1.2.2 (2018-05-01)
==================

* Fix build

1.2.1 (2018-04-30)
==================

* Pin minimal required Python to 3.5.3 (#189)

1.2.0 (2018-04-30)
==================

* Forbid inheritance, replace ``__init__`` with ``__new__`` (#171)

* Support PEP-561 (provide type hinting marker) (#182)

1.1.1 (2018-02-17)
==================

* Fix performance regression: don't encode empty ``netloc`` (#170)

1.1.0 (2018-01-21)
==================

* Make pure Python quoter consistent with Cython version (#162)

1.0.0 (2018-01-15)
==================

* Use fast path if quoted string does not need requoting (#154)

* Speed up quoting/unquoting by ``_Quoter`` and ``_Unquoter`` classes (#155)

* Drop ``yarl.quote`` and ``yarl.unquote`` public functions (#155)

* Add custom string writer, reuse static buffer if available (#157)
  Code is 50-80 times faster than Pure Python version (was 4-5 times faster)

* Don't recode IP zone (#144)

* Support ``encoded=True`` in ``yarl.URL.build()`` (#158)

* Fix updating query with multiple keys (#160)

0.18.0 (2018-01-10)
===================

* Fallback to IDNA 2003 if domain name is not IDNA 2008 compatible (#152)

0.17.0 (2017-12-30)
===================

* Use IDNA 2008 for domain name processing (#149)

0.16.0 (2017-12-07)
===================

* Fix raising ``TypeError`` by ``url.query_string()`` after
  ``url.with_query({})`` (empty mapping) (#141)

0.15.0 (2017-11-23)
===================

* Add ``raw_path_qs`` attribute (#137)

0.14.2 (2017-11-14)
===================

* Restore ``strict`` parameter as no-op in ``quote`` / ``unquote``

0.14.1 (2017-11-13)
===================

* Restore ``strict`` parameter as no-op for sake of compatibility with
  aiohttp 2.2

0.14.0 (2017-11-11)
===================

* Drop strict mode (#123)

* Fix ``"ValueError: Unallowed PCT %"`` when there's a ``"%"`` in the URL (#124)

0.13.0 (2017-10-01)
===================

* Document ``encoded`` parameter (#102)

* Support relative URLs like ``'?key=value'`` (#100)

* Unsafe encoding for QS fixed. Encode ``;`` character in value parameter (#104)

* Process passwords without user names (#95)

0.12.0 (2017-06-26)
===================

* Properly support paths without leading slash in ``URL.with_path()`` (#90)

* Enable type annotation checks

0.11.0 (2017-06-26)
===================

* Normalize path (#86)

* Clear query and fragment parts in ``.with_path()`` (#85)

0.10.3 (2017-06-13)
===================

* Prevent double URL arguments unquoting (#83)

0.10.2 (2017-05-05)
===================

* Unexpected hash behavior (#75)


0.10.1 (2017-05-03)
===================

* Unexpected compare behavior (#73)

* Do not quote or unquote + if not a query string. (#74)


0.10.0 (2017-03-14)
===================

* Added ``URL.build`` class method (#58)

* Added ``path_qs`` attribute (#42)


0.9.8 (2017-02-16)
==================

* Do not quote ``:`` in path


0.9.7 (2017-02-16)
==================

* Load from pickle without _cache (#56)

* Percent-encoded pluses in path variables become spaces (#59)


0.9.6 (2017-02-15)
==================

* Revert backward incompatible change (BaseURL)


0.9.5 (2017-02-14)
==================

* Fix BaseURL rich comparison support


0.9.4 (2017-02-14)
==================

* Use BaseURL


0.9.3 (2017-02-14)
==================

* Added BaseURL


0.9.2 (2017-02-08)
==================

* Remove debug print


0.9.1 (2017-02-07)
==================

* Do not lose tail chars (#45)


0.9.0 (2017-02-07)
==================

* Allow to quote ``%`` in non strict mode (#21)

* Incorrect parsing of query parameters with %3B (;) inside (#34)

* Fix core dumps (#41)

* ``tmpbuf`` - compiling error (#43)

* Added ``URL.update_path()`` method

* Added ``URL.update_query()`` method (#47)


0.8.1 (2016-12-03)
==================

* Fix broken aiohttp: revert back ``quote`` / ``unquote``.


0.8.0 (2016-12-03)
==================

* Support more verbose error messages in ``.with_query()`` (#24)

* Don't percent-encode ``@`` and ``:`` in path (#32)

* Don't expose ``yarl.quote`` and ``yarl.unquote``, these functions are
  part of private API

0.7.1 (2016-11-18)
==================

* Accept not only ``str`` but all classes inherited from ``str`` also (#25)

0.7.0 (2016-11-07)
==================

* Accept ``int`` as value for ``.with_query()``

0.6.0 (2016-11-07)
==================

* Explicitly use UTF8 encoding in ``setup.py`` (#20)
* Properly unquote non-UTF8 strings (#19)

0.5.3 (2016-11-02)
==================

* Don't use ``typing.NamedTuple`` fields but indexes on URL construction

0.5.2 (2016-11-02)
==================

* Inline ``_encode`` class method

0.5.1 (2016-11-02)
==================

* Make URL construction faster by removing extra classmethod calls

0.5.0 (2016-11-02)
==================

* Add Cython optimization for quoting/unquoting
* Provide binary wheels

0.4.3 (2016-09-29)
==================

* Fix typing stubs

0.4.2 (2016-09-29)
==================

* Expose ``quote()`` and ``unquote()`` as public API

0.4.1 (2016-09-28)
==================

* Support empty values in query (``'/path?arg'``)

0.4.0 (2016-09-27)
==================

* Introduce ``relative()`` (#16)

0.3.2 (2016-09-27)
==================

* Typo fixes #15

0.3.1 (2016-09-26)
==================

* Support sequence of pairs as ``with_query()`` parameter

0.3.0 (2016-09-26)
==================

* Introduce ``is_default_port()``

0.2.1 (2016-09-26)
==================

* Raise ValueError for URLs like 'http://:8080/'

0.2.0 (2016-09-18)
==================

* Avoid doubling slashes when joining paths (#13)

* Appending path starting from slash is forbidden (#12)

0.1.4 (2016-09-09)
==================

* Add ``kwargs`` support for ``with_query()`` (#10)

0.1.3 (2016-09-07)
==================

* Document ``with_query()``, ``with_fragment()`` and ``origin()``

* Allow ``None`` for ``with_query()`` and ``with_fragment()``

0.1.2 (2016-09-07)
==================

* Fix links, tune docs theme.

0.1.1 (2016-09-06)
==================

* Update README, old version used obsolete API

0.1.0 (2016-09-06)
==================

* The library was deeply refactored, bytes are gone away but all
  accepted strings are encoded if needed.

0.0.1 (2016-08-30)
==================

* The first release.
