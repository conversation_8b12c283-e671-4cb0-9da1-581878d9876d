../../../bin/openai,sha256=y5DRh5ZT-cyUqbL6JFM3rACkncB2yCdS8BJuTtJQK0U,279
openai-0.28.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai-0.28.1.dist-info/LICENSE,sha256=vLo94hSFHM5G7Vr0LWaYBEYW7qzoh8MjG8eiBHSrY54,1083
openai-0.28.1.dist-info/METADATA,sha256=hyMXbVRPx1yrDfILI-BVzO0fy-Zpd4x0jWiXna3Z9Vc,11656
openai-0.28.1.dist-info/RECORD,,
openai-0.28.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai-0.28.1.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
openai-0.28.1.dist-info/entry_points.txt,sha256=q68fD1MsMkiejaG68n2mKmbuZEwWkq89oniOBBq9fo0,55
openai-0.28.1.dist-info/top_level.txt,sha256=yroxRDiE4kOdI0tEpF1d7ffoQJMxe4i3z_pKoyou9wg,7
openai-0.28.1.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
openai/__init__.py,sha256=rv_DUleSfW1ORJQ3P5F-s93jivhjUbNINjBOOEhEcL4,2799
openai/__pycache__/__init__.cpython-313.pyc,,
openai/__pycache__/_openai_scripts.cpython-313.pyc,,
openai/__pycache__/api_requestor.cpython-313.pyc,,
openai/__pycache__/cli.cpython-313.pyc,,
openai/__pycache__/embeddings_utils.cpython-313.pyc,,
openai/__pycache__/error.cpython-313.pyc,,
openai/__pycache__/object_classes.cpython-313.pyc,,
openai/__pycache__/openai_object.cpython-313.pyc,,
openai/__pycache__/openai_response.cpython-313.pyc,,
openai/__pycache__/upload_progress.cpython-313.pyc,,
openai/__pycache__/util.cpython-313.pyc,,
openai/__pycache__/validators.cpython-313.pyc,,
openai/__pycache__/version.cpython-313.pyc,,
openai/__pycache__/wandb_logger.cpython-313.pyc,,
openai/_openai_scripts.py,sha256=HjggHDCgtoajt4c3fiHgARytTJwiaxoS6tUm2JQxNQ4,2630
openai/api_requestor.py,sha256=mgLv9XLm4tdcTfbGnUyg4_eMYjm5IBdYZcHdIZCUsGA,26820
openai/api_resources/__init__.py,sha256=-ApRXceSmjK--aNhHMHFwEFYG3WpOOW-AuByshh3evk,980
openai/api_resources/__pycache__/__init__.cpython-313.pyc,,
openai/api_resources/__pycache__/audio.cpython-313.pyc,,
openai/api_resources/__pycache__/chat_completion.cpython-313.pyc,,
openai/api_resources/__pycache__/completion.cpython-313.pyc,,
openai/api_resources/__pycache__/customer.cpython-313.pyc,,
openai/api_resources/__pycache__/deployment.cpython-313.pyc,,
openai/api_resources/__pycache__/edit.cpython-313.pyc,,
openai/api_resources/__pycache__/embedding.cpython-313.pyc,,
openai/api_resources/__pycache__/engine.cpython-313.pyc,,
openai/api_resources/__pycache__/error_object.cpython-313.pyc,,
openai/api_resources/__pycache__/file.cpython-313.pyc,,
openai/api_resources/__pycache__/fine_tune.cpython-313.pyc,,
openai/api_resources/__pycache__/fine_tuning.cpython-313.pyc,,
openai/api_resources/__pycache__/image.cpython-313.pyc,,
openai/api_resources/__pycache__/model.cpython-313.pyc,,
openai/api_resources/__pycache__/moderation.cpython-313.pyc,,
openai/api_resources/abstract/__init__.py,sha256=OJuM5gagjZktzrsUQIlv2wBQVLVltzwCZsNDEKO5XTo,639
openai/api_resources/abstract/__pycache__/__init__.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/api_resource.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/createable_api_resource.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/deletable_api_resource.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/engine_api_resource.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/listable_api_resource.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/nested_resource_class_methods.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/paginatable_api_resource.cpython-313.pyc,,
openai/api_resources/abstract/__pycache__/updateable_api_resource.cpython-313.pyc,,
openai/api_resources/abstract/api_resource.py,sha256=wzCww3boKgr0Ru33CIRi8lOCsMEtQJLKlz1HMHIpMkY,5443
openai/api_resources/abstract/createable_api_resource.py,sha256=w6atKaf0aUDe3NuV-wLtbmCxCGPtCxXIkH98yk8s7xQ,2573
openai/api_resources/abstract/deletable_api_resource.py,sha256=9jI4tt7RejbO0aIyLJwivyg3jclr7r55-l50mVFC7GI,1624
openai/api_resources/abstract/engine_api_resource.py,sha256=7ChBvSBqURyzXZ9dEu94x6ixj0UUCDOp7uLGnhII0I8,10107
openai/api_resources/abstract/listable_api_resource.py,sha256=-okp69Lu1OaA9aIkEK2nC-JXRW44vtCKFCN89w_4f60,2678
openai/api_resources/abstract/nested_resource_class_methods.py,sha256=NOKVjqxGES91v1zNv6F9rDS0wjNWh74JE9EFkgx50QY,5804
openai/api_resources/abstract/paginatable_api_resource.py,sha256=RAwBGpU_DqkIPyAYq-fn_a85nUTfPFyioaAy_1r9OMY,3453
openai/api_resources/abstract/updateable_api_resource.py,sha256=cQQd04fI85DTYpOqM_LFuZ9ldDKrkT-MRetkDG5FUU0,534
openai/api_resources/audio.py,sha256=XJLklZKMa0pXn2BjB58T5UUKgskj7NWTIzwAc7gp7kw,9470
openai/api_resources/chat_completion.py,sha256=TTXb7OuBEBjtoz5Rjxw3UTfXcdnL8z-mjFDykj9J23A,1563
openai/api_resources/completion.py,sha256=EDgsMEJXwYyTqB5fGy2Vi6nQeproKZWvNmkdNve5Ykc,1610
openai/api_resources/customer.py,sha256=vX1reXMqDHLssmT2aGev83TIE4NbnzvQ73yrQWyDpFk,539
openai/api_resources/deployment.py,sha256=9K64JZyWxVcvutebf0qguYrSRPb0V_lMALwNsWRqNzs,4055
openai/api_resources/edit.py,sha256=HlE11SFBBJH_ompIBHAYToNc4tIdUu01tZyym7f8LlE,1963
openai/api_resources/embedding.py,sha256=iQBHiBszZPuh4lbiucL9mT_c9ed6DvPatHvyER4ye1Q,3438
openai/api_resources/engine.py,sha256=_bzICgcNFmpdm08yL_W3n-vekkO0AGcCdPGl352HMT8,1665
openai/api_resources/error_object.py,sha256=gKtXIo8mYZDPE2GiJUzQ8LDzLKKEPLqQoyYo_AAqOA4,892
openai/api_resources/experimental/__init__.py,sha256=fLUruA1uO11wqsDNuAKxgx3qVhQyPYC79JoZNTCIq1A,104
openai/api_resources/experimental/__pycache__/__init__.cpython-313.pyc,,
openai/api_resources/experimental/__pycache__/completion_config.cpython-313.pyc,,
openai/api_resources/experimental/completion_config.py,sha256=7vlxmFIkpleNCEUir50NBk31XxnqL0yBFZ5Ah1h9TpQ,274
openai/api_resources/file.py,sha256=0_NHh62OTD926yibTqfevRQyagCBvQQZdv7WNc4c7vM,8435
openai/api_resources/fine_tune.py,sha256=uotwurKyV8wAnyw4x01O2b2t3b--8i_EBSBVbV-2EhE,5263
openai/api_resources/fine_tuning.py,sha256=3iciDEIAfGEXV-IDM1KRyE1OAYWFCntNyYc-aJNDcXk,2261
openai/api_resources/image.py,sha256=cwwVjOFRblykuBC9cLyLsmDBKmfkppF3gwTz2hF7Wgs,8341
openai/api_resources/model.py,sha256=BiXo7j4P6-rhZW8my4eAbD6aIdQ6sAf2Dw9e-U21b_Q,169
openai/api_resources/moderation.py,sha256=XLEVJbP2A5AqcnelbIcXq7-PMqvfPPibOetOf2xiMTA,1376
openai/cli.py,sha256=jTXh91sWBhFGrhBj3G6vGFZslwFOYchh2bPkVEAbiyM,49375
openai/datalib/__init__.py,sha256=cCLTzxxXF1mYb_iC2zX9WMr_VnJWnuWOPxgFvLZKIYE,651
openai/datalib/__pycache__/__init__.cpython-313.pyc,,
openai/datalib/__pycache__/common.cpython-313.pyc,,
openai/datalib/__pycache__/numpy_helper.cpython-313.pyc,,
openai/datalib/__pycache__/pandas_helper.cpython-313.pyc,,
openai/datalib/common.py,sha256=YNnJ_IDOc-lF43LkwFJNftfF5h2n9mhdt6eZfAH976w,258
openai/datalib/numpy_helper.py,sha256=p5o-NYEF0DX5jN9Dy_pRbYpD9M9C1B18konK-WZSOIQ,320
openai/datalib/pandas_helper.py,sha256=WvA3nHCaTgeB5-VgSJlfqfQzVMdmi_hKFRQtamdLzAE,329
openai/embeddings_utils.py,sha256=0LweljppFfBV5GjY_oipGTnVqpicnECqseKYJOX8tWM,8592
openai/error.py,sha256=xJJSg_JWuEKL5WdASVg3wNx27J52tzXN9f86kPkCEII,4298
openai/object_classes.py,sha256=YB1LxGA4lg9L1p1avXfniUw_5vZGsWjoroOOTEf0QYc,431
openai/openai_object.py,sha256=AohBhBMUtdI96e0YBiiqMabG4ZbkcSYIN_q0LZVr_V8,10774
openai/openai_response.py,sha256=GoWVZ5kKB0-p8hEN0_0v8TTOq4drthtNRivQc3VBrsk,836
openai/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/tests/__pycache__/__init__.cpython-313.pyc,,
openai/tests/__pycache__/test_api_requestor.cpython-313.pyc,,
openai/tests/__pycache__/test_endpoints.cpython-313.pyc,,
openai/tests/__pycache__/test_exceptions.cpython-313.pyc,,
openai/tests/__pycache__/test_file_cli.cpython-313.pyc,,
openai/tests/__pycache__/test_long_examples_validator.cpython-313.pyc,,
openai/tests/__pycache__/test_url_composition.cpython-313.pyc,,
openai/tests/__pycache__/test_util.cpython-313.pyc,,
openai/tests/asyncio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/tests/asyncio/__pycache__/__init__.cpython-313.pyc,,
openai/tests/asyncio/__pycache__/test_endpoints.cpython-313.pyc,,
openai/tests/asyncio/test_endpoints.py,sha256=ko3ra_FcwlmkCzxEoVwIyOTYdSNQn7VgSqUGQ7O8kro,2362
openai/tests/test_api_requestor.py,sha256=rU9EJyDrtOqSzDmMaYxf-FKqdaBCFHkzJYGMCQS37EY,3582
openai/tests/test_endpoints.py,sha256=RgYp2Gcz-nkM6AH35MJ_QvNKcpN5IUvIzMth7SzsJN4,2867
openai/tests/test_exceptions.py,sha256=VbKHyi7QAoLi_t6IMuJWSdSkY4zJD4ngrNRjQ89GIjQ,1156
openai/tests/test_file_cli.py,sha256=BIBCaDJ6SZsAtvNqERhEQLDutJjoejgV9GCP3tYcu1A,1418
openai/tests/test_long_examples_validator.py,sha256=hlJSur4VkHxc3kMviBMeuZTATyEqwHCDdCTD0ok_v1g,2016
openai/tests/test_url_composition.py,sha256=d3Jr-bmCFLedEYu3FTYN180WT6wBygRABW8g9R3ccsA,6506
openai/tests/test_util.py,sha256=K268icrNV6XQBi25VSGqpBzOP2bLBoIuSj91NMV4nU0,1786
openai/upload_progress.py,sha256=L723nchd5OQ_tfDFDKOru_rlHdG73uzesOh528wOOcM,1188
openai/util.py,sha256=erOS0iwMOpjIUd1TH2clqXxdA-MU1ll0dC8cBP3DO9w,5366
openai/validators.py,sha256=lqy0NTs1rZS7lt_DFKGtyOmhBVf5LwkmYchsiMKMkBA,34275
openai/version.py,sha256=vFXmbiSH-EgbuYkgT_N-nOEqUw6szrrRCYTI81be4HM,19
openai/wandb_logger.py,sha256=J1HmenSGusATJK30hDgjCwpohNnWM_7YElvWJzk96Fk,10900
