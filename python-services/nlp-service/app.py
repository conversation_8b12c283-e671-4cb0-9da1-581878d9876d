#!/usr/bin/env python3
"""
Natural Language Processing (NLP) Service
Uses OpenAI GPT for command parsing and understanding
"""

import os
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import openai
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configure OpenAI
openai.api_key = os.getenv('OPENAI_API_KEY')
if not openai.api_key:
    logger.warning("OPENAI_API_KEY not set. NLP service will not function properly.")

# Command type mapping
COMMAND_TYPES = {
    'launch_app': 'LAUNCH_APP',
    'run_script': 'RUN_SCRIPT',
    'read_email': 'READ_EMAIL',
    'search_web': 'SEARCH_WEB',
    'system_info': 'SYSTEM_INFO',
    'unknown': 'UNKNOWN'
}

# System prompt for GPT
SYSTEM_PROMPT = """
You are JARVI<PERSON>, an AI assistant that parses voice commands and converts them into structured commands.

Your task is to analyze user input and return a JSON response with the following structure:
{
    "commandType": "LAUNCH_APP|RUN_SCRIPT|READ_EMAIL|SEARCH_WEB|SYSTEM_INFO|UNKNOWN",
    "target": "specific target (app name, script path, etc.)",
    "parameters": {"key": "value"},
    "confidence": 0.0-1.0,
    "responseText": "Natural response to the user"
}

Command Types:
- LAUNCH_APP: Opening applications (e.g., "open Chrome", "launch Spotify")
- RUN_SCRIPT: Running scripts or commands (e.g., "run the backup script")
- READ_EMAIL: Reading emails (e.g., "read my emails", "check inbox")
- SEARCH_WEB: Web searches (e.g., "search for Python tutorials")
- SYSTEM_INFO: System information (e.g., "show system info", "what's my CPU usage")
- UNKNOWN: When the command is unclear or not supported

Examples:
User: "Open Chrome"
Response: {"commandType": "LAUNCH_APP", "target": "Chrome", "parameters": {}, "confidence": 0.95, "responseText": "Opening Chrome browser for you."}

User: "Search for Python tutorials"
Response: {"commandType": "SEARCH_WEB", "target": "web", "parameters": {"query": "Python tutorials"}, "confidence": 0.9, "responseText": "Searching for Python tutorials."}

Always respond with valid JSON only.
"""

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'nlp-service',
        'openai_configured': bool(openai.api_key)
    })

@app.route('/api/parse', methods=['POST'])
def parse_command():
    """
    Parse natural language text into structured commands
    
    Expected JSON payload:
    {
        "text": "user command text",
        "languageCode": "en-US",
        "context": "optional context",
        "model": "gpt-3.5-turbo"
    }
    """
    try:
        if not openai.api_key:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'OpenAI API key not configured',
                'commandType': 'UNKNOWN',
                'target': '',
                'parameters': {},
                'originalText': '',
                'confidence': 0.0,
                'responseText': 'I apologize, but I cannot process commands right now.'
            }), 500

        # Parse request
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'No JSON data provided',
                'commandType': 'UNKNOWN',
                'target': '',
                'parameters': {},
                'originalText': '',
                'confidence': 0.0,
                'responseText': 'I did not receive any command.'
            }), 400

        text = data.get('text', '').strip()
        language_code = data.get('languageCode', 'en-US')
        context = data.get('context', '')
        model = data.get('model', 'gpt-3.5-turbo')

        if not text:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'Text is required',
                'commandType': 'UNKNOWN',
                'target': '',
                'parameters': {},
                'originalText': '',
                'confidence': 0.0,
                'responseText': 'I did not hear any command.'
            }), 400

        logger.info(f"Parsing command: '{text}'")

        # Prepare messages for GPT
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]
        
        if context:
            messages.append({"role": "user", "content": f"Context: {context}"})
        
        messages.append({"role": "user", "content": text})

        # Call OpenAI API
        try:
            response = openai.ChatCompletion.create(
                model=model,
                messages=messages,
                max_tokens=500,
                temperature=0.3
            )
            
            gpt_response = response.choices[0].message.content.strip()
            logger.info(f"GPT response: {gpt_response}")
            
            # Parse GPT response as JSON
            try:
                parsed_command = json.loads(gpt_response)
            except json.JSONDecodeError:
                # If GPT didn't return valid JSON, create a fallback response
                logger.warning(f"GPT returned invalid JSON: {gpt_response}")
                parsed_command = {
                    "commandType": "UNKNOWN",
                    "target": "",
                    "parameters": {},
                    "confidence": 0.1,
                    "responseText": "I'm sorry, I didn't understand that command."
                }

            # Validate and normalize the response
            command_type = parsed_command.get('commandType', 'UNKNOWN')
            target = parsed_command.get('target', '')
            parameters = parsed_command.get('parameters', {})
            confidence = float(parsed_command.get('confidence', 0.5))
            response_text = parsed_command.get('responseText', 'Command processed.')

            # Ensure command type is valid
            if command_type not in COMMAND_TYPES.values():
                command_type = 'UNKNOWN'

            return jsonify({
                'status': 'SUCCESS',
                'commandType': command_type,
                'target': target,
                'parameters': parameters,
                'originalText': text,
                'confidence': confidence,
                'responseText': response_text,
                'errorMessage': None
            })

        except openai.error.OpenAIError as e:
            logger.error(f"OpenAI API error: {e}")
            return jsonify({
                'status': 'ERROR',
                'errorMessage': f'OpenAI API error: {str(e)}',
                'commandType': 'UNKNOWN',
                'target': '',
                'parameters': {},
                'originalText': text,
                'confidence': 0.0,
                'responseText': 'I encountered an error processing your command.'
            }), 500

    except Exception as e:
        logger.error(f"Unexpected error in parse_command: {e}")
        return jsonify({
            'status': 'ERROR',
            'errorMessage': f'Internal server error: {str(e)}',
            'commandType': 'UNKNOWN',
            'target': '',
            'parameters': {},
            'originalText': data.get('text', '') if 'data' in locals() else '',
            'confidence': 0.0,
            'responseText': 'I encountered an unexpected error.'
        }), 500

@app.route('/api/models', methods=['GET'])
def list_models():
    """List available OpenAI models"""
    return jsonify({
        'available_models': ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'],
        'default_model': 'gpt-3.5-turbo',
        'openai_configured': bool(openai.api_key)
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5001))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting NLP service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
