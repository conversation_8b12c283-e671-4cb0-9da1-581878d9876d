#!/bin/bash

# Start all Python microservices for the AI Assistant

echo "Starting AI Assistant Python Microservices..."

# Set environment variables
export OPENAI_API_KEY="${OPENAI_API_KEY:-your-api-key-here}"
export WHISPER_MODEL_SIZE="${WHISPER_MODEL_SIZE:-base}"
export TTS_ENGINE="${TTS_ENGINE:-pyttsx3}"
export USE_OFFLINE_TTS="${USE_OFFLINE_TTS:-true}"

# Function to start a service
start_service() {
    local service_name=$1
    local port=$2
    local service_dir="$service_name-service"
    
    echo "Starting $service_name service on port $port..."
    
    cd "$service_dir" || {
        echo "Error: Directory $service_dir not found"
        return 1
    }
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "Creating virtual environment for $service_name..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install requirements
    echo "Installing requirements for $service_name..."
    pip install -r requirements.txt
    
    # Start the service in background
    export PORT=$port
    nohup python app.py > "../logs/${service_name}.log" 2>&1 &
    echo $! > "../logs/${service_name}.pid"
    
    echo "$service_name service started with PID $(cat "../logs/${service_name}.pid")"
    
    cd ..
}

# Create logs directory
mkdir -p logs

# Start services
start_service "stt" 5000
sleep 2
start_service "nlp" 5001
sleep 2
start_service "tts" 5002

echo ""
echo "All services started!"
echo ""
echo "Service URLs:"
echo "- STT Service: http://localhost:5000"
echo "- NLP Service: http://localhost:5001"
echo "- TTS Service: http://localhost:5002"
echo ""
echo "Health check URLs:"
echo "- STT Health: http://localhost:5000/health"
echo "- NLP Health: http://localhost:5001/health"
echo "- TTS Health: http://localhost:5002/health"
echo ""
echo "Logs are available in the logs/ directory"
echo "To stop all services, run: ./stop-all-services.sh"
