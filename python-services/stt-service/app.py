#!/usr/bin/env python3
"""
Speech-to-Text (STT) Service
Uses OpenAI Whisper for speech recognition
"""

import os
import base64
import tempfile
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import whisper
import torch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Load Whisper model
MODEL_SIZE = os.getenv('WHISPER_MODEL_SIZE', 'base')  # tiny, base, small, medium, large
logger.info(f"Loading Whisper model: {MODEL_SIZE}")

try:
    model = whisper.load_model(MODEL_SIZE)
    logger.info("Whisper model loaded successfully")
except Exception as e:
    logger.error(f"Failed to load Whisper model: {e}")
    model = None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'stt-service',
        'model_loaded': model is not None,
        'model_size': MODEL_SIZE
    })

@app.route('/api/transcribe', methods=['POST'])
def transcribe_audio():
    """
    Transcribe audio to text using Whisper
    
    Expected JSON payload:
    {
        "audioData": "base64_encoded_audio",
        "audioFormat": "wav|mp3|m4a|ogg",
        "languageCode": "en-US"
    }
    """
    try:
        if not model:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'Whisper model not loaded',
                'text': '',
                'confidence': 0.0
            }), 500

        # Parse request
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'No JSON data provided',
                'text': '',
                'confidence': 0.0
            }), 400

        audio_data = data.get('audioData')
        audio_format = data.get('audioFormat', 'wav')
        language_code = data.get('languageCode', 'en')

        if not audio_data:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'audioData is required',
                'text': '',
                'confidence': 0.0
            }), 400

        # Decode base64 audio data
        try:
            audio_bytes = base64.b64decode(audio_data)
        except Exception as e:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': f'Invalid base64 audio data: {str(e)}',
                'text': '',
                'confidence': 0.0
            }), 400

        # Save audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=f'.{audio_format}', delete=False) as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name

        try:
            # Extract language code (e.g., 'en-US' -> 'en')
            language = language_code.split('-')[0] if language_code else 'en'
            
            # Transcribe audio
            logger.info(f"Transcribing audio file: {temp_file_path}")
            result = model.transcribe(
                temp_file_path,
                language=language if language != 'auto' else None,
                fp16=torch.cuda.is_available()
            )
            
            text = result['text'].strip()
            
            # Calculate confidence (Whisper doesn't provide confidence directly)
            # We'll use a simple heuristic based on the presence of text
            confidence = 0.9 if text else 0.0
            
            logger.info(f"Transcription successful: '{text}'")
            
            return jsonify({
                'status': 'SUCCESS',
                'text': text,
                'confidence': confidence,
                'errorMessage': None
            })

        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return jsonify({
                'status': 'ERROR',
                'errorMessage': f'Transcription failed: {str(e)}',
                'text': '',
                'confidence': 0.0
            }), 500

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")

    except Exception as e:
        logger.error(f"Unexpected error in transcribe_audio: {e}")
        return jsonify({
            'status': 'ERROR',
            'errorMessage': f'Internal server error: {str(e)}',
            'text': '',
            'confidence': 0.0
        }), 500

@app.route('/api/models', methods=['GET'])
def list_models():
    """List available Whisper models"""
    models = ['tiny', 'base', 'small', 'medium', 'large']
    return jsonify({
        'available_models': models,
        'current_model': MODEL_SIZE,
        'model_loaded': model is not None
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting STT service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
