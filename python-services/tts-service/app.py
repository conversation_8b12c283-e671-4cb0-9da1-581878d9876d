#!/usr/bin/env python3
"""
Text-to-Speech (TTS) Service
Uses pyttsx3 for offline TTS or gTTS for online TTS
"""

import os
import base64
import tempfile
import logging
import io
from flask import Flask, request, jsonify
from flask_cors import CORS
try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logger.warning("pyttsx3 not available, using gTTS only")

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    logger.warning("gTTS not available")

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("pydub not available, audio format conversion disabled")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# TTS Engine configuration
TTS_ENGINE = os.getenv('TTS_ENGINE', 'pyttsx3')  # 'pyttsx3' or 'gtts'
USE_OFFLINE = os.getenv('USE_OFFLINE_TTS', 'true').lower() == 'true'

# Initialize pyttsx3 engine if using offline TTS
tts_engine = None
if PYTTSX3_AVAILABLE and (USE_OFFLINE or TTS_ENGINE == 'pyttsx3'):
    try:
        tts_engine = pyttsx3.init()
        # Configure voice properties
        voices = tts_engine.getProperty('voices')
        if voices:
            # Try to set a female voice if available
            for voice in voices:
                if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                    tts_engine.setProperty('voice', voice.id)
                    break

        # Set speech rate and volume
        tts_engine.setProperty('rate', 200)  # Speed of speech
        tts_engine.setProperty('volume', 0.9)  # Volume level (0.0 to 1.0)

        logger.info("pyttsx3 TTS engine initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize pyttsx3: {e}")
        tts_engine = None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'tts-service',
        'engine': TTS_ENGINE,
        'offline_available': tts_engine is not None,
        'online_available': GTTS_AVAILABLE,
        'pyttsx3_available': PYTTSX3_AVAILABLE,
        'gtts_available': GTTS_AVAILABLE
    })

@app.route('/api/synthesize', methods=['POST'])
def synthesize_speech():
    """
    Convert text to speech
    
    Expected JSON payload:
    {
        "text": "text to convert to speech",
        "languageCode": "en-US",
        "voice": "default|male|female",
        "speakingRate": 1.0,
        "pitch": 0.0,
        "audioFormat": "mp3|wav"
    }
    """
    try:
        # Parse request
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'No JSON data provided',
                'audioData': '',
                'audioFormat': 'mp3',
                'durationInSeconds': 0.0
            }), 400

        text = data.get('text', '').strip()
        language_code = data.get('languageCode', 'en-US')
        voice = data.get('voice', 'default')
        speaking_rate = float(data.get('speakingRate', 1.0))
        pitch = float(data.get('pitch', 0.0))
        audio_format = data.get('audioFormat', 'mp3')

        if not text:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'Text is required',
                'audioData': '',
                'audioFormat': audio_format,
                'durationInSeconds': 0.0
            }), 400

        logger.info(f"Synthesizing text: '{text[:50]}...' (length: {len(text)})")

        # Choose TTS method based on configuration and availability
        if USE_OFFLINE and tts_engine and PYTTSX3_AVAILABLE:
            audio_data, duration = synthesize_with_pyttsx3(text, voice, speaking_rate, audio_format)
        elif GTTS_AVAILABLE:
            audio_data, duration = synthesize_with_gtts(text, language_code, audio_format)
        else:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'No TTS engine available',
                'audioData': '',
                'audioFormat': audio_format,
                'durationInSeconds': 0.0
            }), 500

        if audio_data:
            return jsonify({
                'status': 'SUCCESS',
                'audioData': audio_data,
                'audioFormat': audio_format,
                'durationInSeconds': duration,
                'errorMessage': None
            })
        else:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'Failed to synthesize speech',
                'audioData': '',
                'audioFormat': audio_format,
                'durationInSeconds': 0.0
            }), 500

    except Exception as e:
        logger.error(f"Unexpected error in synthesize_speech: {e}")
        return jsonify({
            'status': 'ERROR',
            'errorMessage': f'Internal server error: {str(e)}',
            'audioData': '',
            'audioFormat': 'mp3',
            'durationInSeconds': 0.0
        }), 500

def synthesize_with_pyttsx3(text: str, voice: str, speaking_rate: float, audio_format: str):
    """Synthesize speech using pyttsx3 (offline)"""
    try:
        if not tts_engine:
            raise Exception("pyttsx3 engine not available")

        # Adjust speaking rate
        rate = int(200 * speaking_rate)  # Base rate is 200
        tts_engine.setProperty('rate', rate)

        # Set voice if specified
        if voice != 'default':
            voices = tts_engine.getProperty('voices')
            for v in voices:
                if voice.lower() in v.name.lower():
                    tts_engine.setProperty('voice', v.id)
                    break

        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=f'.{audio_format}', delete=False) as temp_file:
            temp_file_path = temp_file.name

        # Save speech to file
        tts_engine.save_to_file(text, temp_file_path)
        tts_engine.runAndWait()

        # Read the generated audio file
        with open(temp_file_path, 'rb') as audio_file:
            audio_bytes = audio_file.read()

        # Clean up
        os.unlink(temp_file_path)

        # Convert to base64
        audio_data = base64.b64encode(audio_bytes).decode('utf-8')
        
        # Estimate duration (rough calculation: ~150 words per minute)
        word_count = len(text.split())
        duration = (word_count / 150.0) * 60.0 / speaking_rate

        logger.info(f"pyttsx3 synthesis successful, duration: {duration:.2f}s")
        return audio_data, duration

    except Exception as e:
        logger.error(f"pyttsx3 synthesis failed: {e}")
        return None, 0.0

def synthesize_with_gtts(text: str, language_code: str, audio_format: str):
    """Synthesize speech using gTTS (online)"""
    try:
        # Extract language code (e.g., 'en-US' -> 'en')
        lang = language_code.split('-')[0] if language_code else 'en'
        
        # Create gTTS object
        tts = gTTS(text=text, lang=lang, slow=False)
        
        # Save to bytes buffer
        audio_buffer = io.BytesIO()
        tts.write_to_fp(audio_buffer)
        audio_buffer.seek(0)
        
        audio_bytes = audio_buffer.getvalue()
        
        # Convert format if needed
        if audio_format != 'mp3':
            audio_bytes = convert_audio_format(audio_bytes, 'mp3', audio_format)
        
        # Convert to base64
        audio_data = base64.b64encode(audio_bytes).decode('utf-8')
        
        # Estimate duration (rough calculation)
        word_count = len(text.split())
        duration = (word_count / 150.0) * 60.0  # ~150 words per minute

        logger.info(f"gTTS synthesis successful, duration: {duration:.2f}s")
        return audio_data, duration

    except Exception as e:
        logger.error(f"gTTS synthesis failed: {e}")
        return None, 0.0

def convert_audio_format(audio_bytes: bytes, from_format: str, to_format: str):
    """Convert audio from one format to another using pydub"""
    if not PYDUB_AVAILABLE:
        logger.warning("pydub not available, returning original audio format")
        return audio_bytes

    try:
        # Load audio with pydub
        audio = AudioSegment.from_file(io.BytesIO(audio_bytes), format=from_format)

        # Export to desired format
        output_buffer = io.BytesIO()
        audio.export(output_buffer, format=to_format)
        output_buffer.seek(0)

        return output_buffer.getvalue()

    except Exception as e:
        logger.error(f"Audio format conversion failed: {e}")
        return audio_bytes  # Return original if conversion fails

@app.route('/api/voices', methods=['GET'])
def list_voices():
    """List available voices"""
    voices_info = []
    
    if tts_engine:
        try:
            voices = tts_engine.getProperty('voices')
            for voice in voices:
                voices_info.append({
                    'id': voice.id,
                    'name': voice.name,
                    'languages': getattr(voice, 'languages', []),
                    'gender': 'female' if 'female' in voice.name.lower() else 'male'
                })
        except Exception as e:
            logger.error(f"Error listing voices: {e}")
    
    return jsonify({
        'offline_voices': voices_info,
        'online_languages': ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        'engine': TTS_ENGINE,
        'offline_available': tts_engine is not None
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5002))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting TTS service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
