#!/usr/bin/env python3
"""
Simple Text-to-Speech (TTS) Service
Uses only gTTS for online TTS
"""

import os
import base64
import tempfile
import logging
import io
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
    logger.info("gTTS is available")
except ImportError:
    GTTS_AVAILABLE = False
    logger.error("gTTS not available")

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'tts-service-simple',
        'gtts_available': GTTS_AVAILABLE
    })

@app.route('/api/synthesize', methods=['POST'])
def synthesize_speech():
    """
    Convert text to speech using gTTS
    """
    try:
        if not GTTS_AVAILABLE:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'gTTS not available',
                'audioData': '',
                'audioFormat': 'mp3',
                'durationInSeconds': 0.0
            }), 500

        # Parse request
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'No JSON data provided',
                'audioData': '',
                'audioFormat': 'mp3',
                'durationInSeconds': 0.0
            }), 400

        text = data.get('text', '').strip()
        language_code = data.get('languageCode', 'en-US')
        audio_format = data.get('audioFormat', 'mp3')

        if not text:
            return jsonify({
                'status': 'ERROR',
                'errorMessage': 'Text is required',
                'audioData': '',
                'audioFormat': audio_format,
                'durationInSeconds': 0.0
            }), 400

        logger.info(f"Synthesizing text: '{text[:50]}...' (length: {len(text)})")

        # Extract language code (e.g., 'en-US' -> 'en')
        lang = language_code.split('-')[0] if language_code else 'en'
        
        # Create gTTS object
        tts = gTTS(text=text, lang=lang, slow=False)
        
        # Save to bytes buffer
        audio_buffer = io.BytesIO()
        tts.write_to_fp(audio_buffer)
        audio_buffer.seek(0)
        
        audio_bytes = audio_buffer.getvalue()
        
        # Convert to base64
        audio_data = base64.b64encode(audio_bytes).decode('utf-8')
        
        # Estimate duration (rough calculation)
        word_count = len(text.split())
        duration = (word_count / 150.0) * 60.0  # ~150 words per minute

        logger.info(f"gTTS synthesis successful, duration: {duration:.2f}s")
        
        return jsonify({
            'status': 'SUCCESS',
            'audioData': audio_data,
            'audioFormat': 'mp3',
            'durationInSeconds': duration,
            'errorMessage': None
        })

    except Exception as e:
        logger.error(f"Unexpected error in synthesize_speech: {e}")
        return jsonify({
            'status': 'ERROR',
            'errorMessage': f'Internal server error: {str(e)}',
            'audioData': '',
            'audioFormat': 'mp3',
            'durationInSeconds': 0.0
        }), 500

@app.route('/api/voices', methods=['GET'])
def list_voices():
    """List available voices"""
    return jsonify({
        'online_languages': ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
        'engine': 'gtts',
        'gtts_available': GTTS_AVAILABLE
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5002))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting simple TTS service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
