#!/bin/bash

# Stop all Python microservices for the AI Assistant

echo "Stopping AI Assistant Python Microservices..."

# Function to stop a service
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        echo "Stopping $service_name service (PID: $pid)..."
        
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            sleep 2
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo "Force killing $service_name service..."
                kill -9 "$pid"
            fi
            
            echo "$service_name service stopped"
        else
            echo "$service_name service was not running"
        fi
        
        rm -f "$pid_file"
    else
        echo "No PID file found for $service_name service"
    fi
}

# Stop services
stop_service "stt"
stop_service "nlp"
stop_service "tts"

# Also kill any remaining Python processes on these ports
echo "Checking for any remaining processes on ports 5000-5002..."
for port in 5000 5001 5002; do
    pid=$(lsof -ti:$port 2>/dev/null)
    if [ -n "$pid" ]; then
        echo "Killing process on port $port (PID: $pid)"
        kill -9 "$pid" 2>/dev/null
    fi
done

echo "All services stopped!"
