# JARVIS - Personal AI Assistant

A personal AI assistant inspired by <PERSON><PERSON><PERSON><PERSON> from Iron Man that listens to voice commands, understands natural language, responds with speech, and performs tasks such as opening apps, reading emails, and executing scripts.

## Architecture

The system consists of:
- **Java Spring Boot Backend**: Main orchestration and command execution
- **Python Microservices**: Speech processing and AI capabilities
  - STT Service (Speech-to-Text) using OpenAI Whisper
  - NLP Service (Natural Language Processing) using OpenAI GPT
  - TTS Service (Text-to-Speech) using pyttsx3/gTTS

## Features

- 🎤 **Voice Command Processing**: Complete voice-to-voice interaction
- 🧠 **Natural Language Understanding**: Powered by OpenAI GPT
- 🗣️ **Speech Synthesis**: High-quality text-to-speech
- 💻 **OS-level Operations**: Launch apps, run scripts, system info
- 🔍 **Web Search**: Search the web with voice commands
- 📧 **Email Integration**: Read and manage emails (placeholder)
- 🎯 **Wake Word Detection**: Responds to "Jarvis" wake word
- 🌐 **REST API**: Full REST API for integration

## Quick Start

### Prerequisites

- Java 17+
- Maven 3.6+
- Python 3.8+
- OpenAI API Key (for NLP service)

### 1. Start Python Microservices

```bash
cd python-services
export OPENAI_API_KEY="your-openai-api-key-here"
./start-all-services.sh
```

This will start:
- STT Service on port 5000
- NLP Service on port 5001  
- TTS Service on port 5002

### 2. Start Java Spring Boot Application

```bash
mvn spring-boot:run
```

The main application will start on port 8080.

### 3. Test the System

#### Health Check
```bash
curl http://localhost:8080/indu/api/assistant/status
```

#### Text Command
```bash
curl -X POST http://localhost:8080/indu/api/assistant/text \
  -H "Content-Type: application/json" \
  -d '{"text": "open Chrome"}'
```

#### Voice Command (upload audio file)
```bash
curl -X POST http://localhost:8080/indu/api/audio/voice-command \
  -F "audio=@your-audio-file.wav"
```

## API Endpoints

### Assistant Controller (`/api/assistant`)
- `GET /status` - Check if assistant is online
- `POST /wake` - Wake word detection
- `POST /command` - Execute structured command
- `POST /text` - Process text command through NLP pipeline
- `POST /error` - Report errors

### Audio Controller (`/api/audio`)
- `POST /voice-command` - Process voice command (upload audio file)
- `POST /text-to-speech` - Convert text to speech
- `POST /upload-test` - Test audio upload
- `POST /wake-word` - Handle wake word detection

## Configuration

### Java Application (`application.properties`)
```properties
# Server configuration
server.port=8080
server.servlet.context-path=/indu

# API Service URLs
api.stt.base-url=http://localhost:5000
api.nlp.base-url=http://localhost:5001
api.tts.base-url=http://localhost:5002

# Assistant settings
assistant.default-language=en-US
assistant.default-voice=default
assistant.wake-word=jarvis
assistant.audio.storage-path=./audio-files
```

### Python Services Environment Variables
```bash
export OPENAI_API_KEY="your-api-key"
export WHISPER_MODEL_SIZE="base"  # tiny, base, small, medium, large
export TTS_ENGINE="pyttsx3"       # pyttsx3 or gtts
export USE_OFFLINE_TTS="true"     # true for offline, false for online
```

## Supported Commands

The assistant can understand and execute various types of commands:

### Application Launch
- "Open Chrome"
- "Launch Spotify"
- "Start IntelliJ IDEA"

### Script Execution
- "Run the backup script"
- "Execute deploy.sh"

### System Information
- "Show system info"
- "What's my CPU usage?"
- "Check memory usage"

### Web Search
- "Search for Python tutorials"
- "Look up Spring Boot documentation"

### Email (Placeholder)
- "Read my emails"
- "Check inbox"

## Development

### Project Structure
```
├── src/main/java/com/rishi/indu/
│   ├── controller/          # REST controllers
│   ├── service/            # Business logic services
│   ├── model/              # Data models
│   └── config/             # Configuration classes
├── python-services/
│   ├── stt-service/        # Speech-to-Text service
│   ├── nlp-service/        # Natural Language Processing
│   └── tts-service/        # Text-to-Speech service
└── README.md
```

### Adding New Command Types

1. Add new command type to `Command.CommandType` enum
2. Implement handler in `CommandExecutor`
3. Update NLP service prompt to recognize new commands
4. Add tests

### Running Tests

```bash
# Java tests
mvn test

# Python service tests (if implemented)
cd python-services/stt-service && python -m pytest
```

## Troubleshooting

### Common Issues

1. **OpenAI API Key Not Set**
   - Set the `OPENAI_API_KEY` environment variable
   - Check NLP service logs: `tail -f python-services/logs/nlp.log`

2. **Audio Processing Issues**
   - Ensure ffmpeg is installed for audio format conversion
   - Check supported audio formats: wav, mp3, m4a, ogg

3. **Service Connection Issues**
   - Verify all services are running: check health endpoints
   - Check firewall settings for ports 5000-5002

4. **Whisper Model Download**
   - First run may take time to download Whisper model
   - Check STT service logs for download progress

### Logs

- Java application: Console output or configure logging
- Python services: `python-services/logs/` directory

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- OpenAI for Whisper and GPT APIs
- Spring Boot team for the excellent framework
- Iron Man for the inspiration 🤖
