spring.application.name=indu

# Server configuration
server.port=8080
server.servlet.context-path=/indu

# Logging configuration
logging.level.root=INFO
logging.level.com.rishi.indu=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# API Service configurations
# Speech-to-Text (STT) service
api.stt.base-url=http://localhost:5000
api.stt.endpoint=/api/transcribe

# Natural Language Processing (NLP) service
api.nlp.base-url=http://localhost:5001
api.nlp.endpoint=/api/parse

# Text-to-Speech (TTS) service
api.tts.base-url=http://localhost:5002
api.tts.endpoint=/api/synthesize

# Default language and voice settings
assistant.default-language=en-US
assistant.default-voice=default
assistant.wake-word=jarvis
