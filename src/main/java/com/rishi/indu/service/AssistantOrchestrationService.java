package com.rishi.indu.service;

import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import com.rishi.indu.model.api.*;
import com.rishi.indu.service.api.NlpService;
import com.rishi.indu.service.api.SttService;
import com.rishi.indu.service.api.TtsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * Main orchestration service that coordinates the complete voice assistant flow.
 * Handles the pipeline: Audio Input → STT → NLP → Command Execution → TTS → Audio Output
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssistantOrchestrationService {

    private final SttService sttService;
    private final NlpService nlpService;
    private final TtsService ttsService;
    private final CommandExecutor commandExecutor;
    private final AudioProcessingService audioProcessingService;

    @Value("${assistant.default-language:en-US}")
    private String defaultLanguage;

    @Value("${assistant.default-voice:default}")
    private String defaultVoice;

    /**
     * Processes audio input through the complete voice assistant pipeline.
     *
     * @param audioData Base64 encoded audio data
     * @param audioFormat Audio format (wav, mp3, etc.)
     * @return Mono containing the TTS response with synthesized speech
     */
    public Mono<TtsResponse> processVoiceCommand(String audioData, String audioFormat) {
        log.info("Starting voice command processing pipeline");

        return transcribeAudio(audioData, audioFormat)
                .flatMap(this::parseTextToCommand)
                .map(this::executeCommand)
                .flatMap(this::synthesizeResponse)
                .doOnSuccess(response -> log.info("Voice command processing completed successfully"))
                .doOnError(error -> log.error("Error in voice command processing pipeline", error));
    }

    /**
     * Processes text input through the NLP and command execution pipeline.
     *
     * @param text The text command to process
     * @return Response from command execution
     */
    public Mono<Response> processTextCommand(String text) {
        log.info("Processing text command: {}", text);

        return parseTextToCommand(text)
                .map(this::executeCommand)
                .doOnSuccess(response -> log.info("Text command processing completed"))
                .doOnError(error -> log.error("Error in text command processing", error));
    }

    /**
     * Transcribes audio to text using the STT service.
     *
     * @param audioData Base64 encoded audio data
     * @param audioFormat Audio format
     * @return Mono containing the transcribed text
     */
    private Mono<String> transcribeAudio(String audioData, String audioFormat) {
        log.info("Transcribing audio to text");

        SttRequest sttRequest = SttRequest.builder()
                .audioData(audioData)
                .audioFormat(audioFormat)
                .languageCode(defaultLanguage)
                .build();

        return sttService.sendRequest(sttRequest)
                .map(sttResponse -> {
                    if (sttResponse.getStatus() == SttResponse.Status.SUCCESS) {
                        log.info("Audio transcribed successfully: {}", sttResponse.getText());
                        return sttResponse.getText();
                    } else {
                        throw new RuntimeException("STT failed: " + sttResponse.getErrorMessage());
                    }
                });
    }

    /**
     * Parses text to extract command using the NLP service.
     *
     * @param text The text to parse
     * @return Mono containing the parsed command
     */
    private Mono<Command> parseTextToCommand(String text) {
        log.info("Parsing text to command: {}", text);

        NlpRequest nlpRequest = NlpRequest.builder()
                .text(text)
                .languageCode(defaultLanguage)
                .model("gpt-3.5-turbo")
                .build();

        return nlpService.sendRequest(nlpRequest)
                .map(nlpResponse -> {
                    if (nlpResponse.getStatus() == NlpResponse.Status.SUCCESS) {
                        log.info("Text parsed successfully: command type={}, target={}", 
                                nlpResponse.getCommandType(), nlpResponse.getTarget());
                        return nlpResponse.toCommand();
                    } else {
                        throw new RuntimeException("NLP failed: " + nlpResponse.getErrorMessage());
                    }
                });
    }

    /**
     * Executes the parsed command.
     *
     * @param command The command to execute
     * @return Response from command execution
     */
    private Response executeCommand(Command command) {
        log.info("Executing command: {}", command);
        return commandExecutor.executeCommand(command);
    }

    /**
     * Synthesizes the response text to speech using the TTS service.
     *
     * @param response The response to synthesize
     * @return Mono containing the TTS response with audio data
     */
    private Mono<TtsResponse> synthesizeResponse(Response response) {
        log.info("Synthesizing response to speech: {}", response.getMessage());

        TtsRequest ttsRequest = TtsRequest.builder()
                .text(response.getMessage())
                .languageCode(defaultLanguage)
                .voice(defaultVoice)
                .speakingRate(1.0)
                .pitch(0.0)
                .audioFormat("mp3")
                .build();

        return ttsService.sendRequest(ttsRequest)
                .map(ttsResponse -> {
                    if (ttsResponse.getStatus() == TtsResponse.Status.SUCCESS) {
                        log.info("Response synthesized successfully");
                        return ttsResponse;
                    } else {
                        throw new RuntimeException("TTS failed: " + ttsResponse.getErrorMessage());
                    }
                });
    }

    /**
     * Handles wake word detection and prepares the assistant for listening.
     *
     * @return Response indicating the assistant is ready to listen
     */
    public Response handleWakeWord() {
        log.info("Wake word detected - assistant is now listening");
        return Response.builder()
                .status(Response.Status.SUCCESS)
                .message("I'm listening. How can I help you?")
                .build();
    }
}
