package com.rishi.indu.service.api;

import com.rishi.indu.model.api.SttRequest;
import com.rishi.indu.model.api.SttResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * Service for integrating with the Speech-to-Text (STT) Python microservice.
 * Sends audio data to the STT service and receives transcribed text.
 */
@Service
@Slf4j
public class SttService implements ApiService<SttRequest, SttResponse> {

    private final WebClient webClient;
    private final String baseUrl;
    private final String endpoint;

    /**
     * Constructor for SttService.
     *
     * @param webClient Configured WebClient for STT service
     * @param baseUrl The base URL of the STT service
     * @param endpoint The endpoint for STT operations
     */
    public SttService(
            @Qualifier("sttWebClient") WebClient webClient,
            @Value("${api.stt.base-url:http://localhost:5000}") String baseUrl,
            @Value("${api.stt.endpoint:/api/transcribe}") String endpoint) {
        this.baseUrl = baseUrl;
        this.endpoint = endpoint;
        this.webClient = webClient.mutate().baseUrl(baseUrl).build();
        log.info("Initialized STT service with base URL: {} and endpoint: {}", baseUrl, endpoint);
    }

    /**
     * Sends audio data to the STT service for transcription.
     *
     * @param request The STT request containing audio data
     * @return A Mono containing the STT response with transcribed text
     */
    @Override
    public Mono<SttResponse> sendRequest(SttRequest request) {
        log.info("Sending STT request to {}{}", baseUrl, endpoint);
        
        return webClient.post()
                .uri(endpoint)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(SttResponse.class)
                .doOnSuccess(response -> log.info("Received STT response: {}", response))
                .doOnError(error -> log.error("Error in STT request", error))
                .onErrorResume(error -> {
                    log.error("Error in STT request", error);
                    return Mono.just(SttResponse.builder()
                            .status(SttResponse.Status.ERROR)
                            .errorMessage("Failed to communicate with STT service: " + error.getMessage())
                            .build());
                });
    }

    /**
     * Gets the base URL of the STT service.
     *
     * @return The base URL
     */
    @Override
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * Gets the endpoint for STT operations.
     *
     * @return The endpoint
     */
    @Override
    public String getEndpoint() {
        return endpoint;
    }
}