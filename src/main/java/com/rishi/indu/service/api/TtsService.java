package com.rishi.indu.service.api;

import com.rishi.indu.model.api.TtsRequest;
import com.rishi.indu.model.api.TtsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * Service for integrating with the Text-to-Speech (TTS) Python microservice.
 * Sends text to the TTS service and receives audio data.
 */
@Service
@Slf4j
public class TtsService implements ApiService<TtsRequest, TtsResponse> {

    private final WebClient webClient;
    private final String baseUrl;
    private final String endpoint;

    /**
     * Constructor for TtsService.
     *
     * @param baseUrl The base URL of the TTS service
     * @param endpoint The endpoint for TTS operations
     */
    public TtsService(
            @Value("${api.tts.base-url:http://localhost:5002}") String baseUrl,
            @Value("${api.tts.endpoint:/api/synthesize}") String endpoint) {
        this.baseUrl = baseUrl;
        this.endpoint = endpoint;
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();
        log.info("Initialized TTS service with base URL: {} and endpoint: {}", baseUrl, endpoint);
    }

    /**
     * Sends text to the TTS service for speech synthesis.
     *
     * @param request The TTS request containing text to synthesize
     * @return A Mono containing the TTS response with audio data
     */
    @Override
    public Mono<TtsResponse> sendRequest(TtsRequest request) {
        log.info("Sending TTS request to {}{}: text length={}", baseUrl, endpoint, 
                request.getText() != null ? request.getText().length() : 0);
        
        return webClient.post()
                .uri(endpoint)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(TtsResponse.class)
                .doOnSuccess(response -> log.info("Received TTS response: status={}, audio format={}, duration={}s", 
                        response.getStatus(), response.getAudioFormat(), response.getDurationInSeconds()))
                .doOnError(error -> log.error("Error in TTS request", error))
                .onErrorResume(error -> {
                    log.error("Error in TTS request", error);
                    return Mono.just(TtsResponse.builder()
                            .status(TtsResponse.Status.ERROR)
                            .errorMessage("Failed to communicate with TTS service: " + error.getMessage())
                            .build());
                });
    }

    /**
     * Gets the base URL of the TTS service.
     *
     * @return The base URL
     */
    @Override
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * Gets the endpoint for TTS operations.
     *
     * @return The endpoint
     */
    @Override
    public String getEndpoint() {
        return endpoint;
    }
}