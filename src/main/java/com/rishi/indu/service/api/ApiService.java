package com.rishi.indu.service.api;

import reactor.core.publisher.Mono;

/**
 * Common interface for API services.
 * Defines methods for sending requests to external services.
 *
 * @param <T> The type of request
 * @param <R> The type of response
 */
public interface ApiService<T, R> {
    
    /**
     * Sends a request to the external service and returns a response.
     *
     * @param request The request to send
     * @return A Mono containing the response
     */
    Mono<R> sendRequest(T request);
    
    /**
     * Gets the base URL of the external service.
     *
     * @return The base URL
     */
    String getBaseUrl();
    
    /**
     * Gets the endpoint for the specific operation.
     *
     * @return The endpoint
     */
    String getEndpoint();
}