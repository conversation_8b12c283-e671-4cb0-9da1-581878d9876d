package com.rishi.indu.service.api;

import com.rishi.indu.model.api.NlpRequest;
import com.rishi.indu.model.api.NlpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * Service for integrating with the Natural Language Processing (NLP) Python microservice.
 * Sends text to the NLP service and receives parsed commands.
 */
@Service
@Slf4j
public class NlpService implements ApiService<NlpRequest, NlpResponse> {

    private final WebClient webClient;
    private final String baseUrl;
    private final String endpoint;

    /**
     * Constructor for NlpService.
     *
     * @param baseUrl The base URL of the NLP service
     * @param endpoint The endpoint for NLP operations
     */
    public NlpService(
            @Value("${api.nlp.base-url:http://localhost:5001}") String baseUrl,
            @Value("${api.nlp.endpoint:/api/parse}") String endpoint) {
        this.baseUrl = baseUrl;
        this.endpoint = endpoint;
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();
        log.info("Initialized NLP service with base URL: {} and endpoint: {}", baseUrl, endpoint);
    }

    /**
     * Sends text to the NLP service for parsing and command extraction.
     *
     * @param request The NLP request containing text to parse
     * @return A Mono containing the NLP response with parsed command
     */
    @Override
    public Mono<NlpResponse> sendRequest(NlpRequest request) {
        log.info("Sending NLP request to {}{}: {}", baseUrl, endpoint, request.getText());
        
        return webClient.post()
                .uri(endpoint)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(NlpResponse.class)
                .doOnSuccess(response -> log.info("Received NLP response: command type={}, target={}", 
                        response.getCommandType(), response.getTarget()))
                .doOnError(error -> log.error("Error in NLP request", error))
                .onErrorResume(error -> {
                    log.error("Error in NLP request", error);
                    return Mono.just(NlpResponse.builder()
                            .status(NlpResponse.Status.ERROR)
                            .errorMessage("Failed to communicate with NLP service: " + error.getMessage())
                            .originalText(request.getText())
                            .build());
                });
    }

    /**
     * Gets the base URL of the NLP service.
     *
     * @return The base URL
     */
    @Override
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * Gets the endpoint for NLP operations.
     *
     * @return The endpoint
     */
    @Override
    public String getEndpoint() {
        return endpoint;
    }
}