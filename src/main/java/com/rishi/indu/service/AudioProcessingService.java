package com.rishi.indu.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.UUID;

/**
 * Service for handling audio file operations.
 * Manages audio file storage, conversion, and validation.
 */
@Service
@Slf4j
public class AudioProcessingService {

    @Value("${assistant.audio.storage-path:./audio-files}")
    private String audioStoragePath;

    @Value("${assistant.audio.max-file-size:10485760}") // 10MB default
    private long maxFileSize;

    /**
     * Saves an uploaded audio file and returns its Base64 encoded content.
     *
     * @param file The uploaded audio file
     * @return Base64 encoded audio data
     * @throws IOException If file operations fail
     */
    public String saveAndEncodeAudioFile(MultipartFile file) throws IOException {
        validateAudioFile(file);
        
        // Create storage directory if it doesn't exist
        Path storagePath = Paths.get(audioStoragePath);
        if (!Files.exists(storagePath)) {
            Files.createDirectories(storagePath);
        }

        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String filename = UUID.randomUUID().toString() + "." + extension;
        Path filePath = storagePath.resolve(filename);

        // Save file
        file.transferTo(filePath.toFile());
        log.info("Audio file saved: {}", filePath);

        // Read and encode file
        byte[] audioBytes = Files.readAllBytes(filePath);
        String encodedAudio = Base64.getEncoder().encodeToString(audioBytes);

        // Clean up temporary file (optional - you might want to keep it)
        // Files.delete(filePath);

        return encodedAudio;
    }

    /**
     * Saves Base64 encoded audio data to a file.
     *
     * @param audioData Base64 encoded audio data
     * @param audioFormat Audio format (mp3, wav, etc.)
     * @return Path to the saved audio file
     * @throws IOException If file operations fail
     */
    public Path saveAudioFromBase64(String audioData, String audioFormat) throws IOException {
        // Create storage directory if it doesn't exist
        Path storagePath = Paths.get(audioStoragePath);
        if (!Files.exists(storagePath)) {
            Files.createDirectories(storagePath);
        }

        // Generate unique filename
        String filename = UUID.randomUUID().toString() + "." + audioFormat;
        Path filePath = storagePath.resolve(filename);

        // Decode and save audio data
        byte[] audioBytes = Base64.getDecoder().decode(audioData);
        Files.write(filePath, audioBytes);

        log.info("Audio file saved from Base64: {}", filePath);
        return filePath;
    }

    /**
     * Reads an audio file and returns its Base64 encoded content.
     *
     * @param filePath Path to the audio file
     * @return Base64 encoded audio data
     * @throws IOException If file operations fail
     */
    public String encodeAudioFile(Path filePath) throws IOException {
        byte[] audioBytes = Files.readAllBytes(filePath);
        return Base64.getEncoder().encodeToString(audioBytes);
    }

    /**
     * Validates an uploaded audio file.
     *
     * @param file The file to validate
     * @throws IllegalArgumentException If validation fails
     */
    private void validateAudioFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("Audio file is empty");
        }

        if (file.getSize() > maxFileSize) {
            throw new IllegalArgumentException("Audio file size exceeds maximum allowed size: " + maxFileSize);
        }

        String contentType = file.getContentType();
        if (contentType == null || !isValidAudioFormat(contentType)) {
            throw new IllegalArgumentException("Invalid audio format. Supported formats: wav, mp3, m4a, ogg");
        }
    }

    /**
     * Checks if the content type represents a valid audio format.
     *
     * @param contentType The content type to check
     * @return true if valid audio format, false otherwise
     */
    private boolean isValidAudioFormat(String contentType) {
        return contentType.startsWith("audio/") || 
               contentType.equals("application/octet-stream"); // Some audio files might have this type
    }

    /**
     * Extracts file extension from filename.
     *
     * @param filename The filename
     * @return File extension without the dot
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "wav"; // default extension
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "wav"; // default extension
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * Cleans up old audio files to prevent disk space issues.
     * This method can be called periodically to remove old files.
     *
     * @param maxAgeHours Maximum age of files to keep (in hours)
     */
    public void cleanupOldAudioFiles(int maxAgeHours) {
        try {
            Path storagePath = Paths.get(audioStoragePath);
            if (!Files.exists(storagePath)) {
                return;
            }

            long cutoffTime = System.currentTimeMillis() - (maxAgeHours * 60 * 60 * 1000L);

            Files.list(storagePath)
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        try {
                            return Files.getLastModifiedTime(path).toMillis() < cutoffTime;
                        } catch (IOException e) {
                            log.warn("Error checking file modification time: {}", path, e);
                            return false;
                        }
                    })
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            log.info("Deleted old audio file: {}", path);
                        } catch (IOException e) {
                            log.warn("Error deleting old audio file: {}", path, e);
                        }
                    });

        } catch (IOException e) {
            log.error("Error during audio file cleanup", e);
        }
    }
}
