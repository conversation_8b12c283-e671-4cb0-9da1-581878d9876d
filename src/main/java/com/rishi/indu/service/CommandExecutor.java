package com.rishi.indu.service;

import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Service responsible for executing commands based on their type.
 * Handles OS-level operations like launching applications and running scripts.
 */
@Service
@Slf4j
public class CommandExecutor {

    /**
     * Executes a command and returns a response.
     *
     * @param command The command to execute
     * @return A response with the result of the command execution
     */
    public Response executeCommand(Command command) {
        log.info("Executing command: {}", command);
        
        try {
            switch (command.getType()) {
                case LAUNCH_APP:
                    return launchApplication(command.getTarget(), command.getParameters());
                case RUN_SCRIPT:
                    return runScript(command.getTarget(), command.getParameters());
                case READ_EMAIL:
                    return readEmail(command.getParameters());
                case SEARCH_WEB:
                    return searchWeb(command.getParameters());
                case SYSTEM_INFO:
                    return getSystemInfo();
                case UNKNOWN:
                default:
                    return Response.builder()
                            .status(Response.Status.ERROR)
                            .message("I'm sorry, I don't understand that command.")
                            .build();
            }
        } catch (Exception e) {
            log.error("Error executing command", e);
            return Response.builder()
                    .status(Response.Status.ERROR)
                    .message("I encountered an error while executing your command: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Launches an application asynchronously.
     *
     * @param appName The name of the application to launch
     * @param parameters Additional parameters for launching the application
     * @return A response indicating the result of the operation
     */
    private Response launchApplication(String appName, Map<String, String> parameters) {
        log.info("Launching application: {}", appName);
        
        try {
            List<String> command = new ArrayList<>();
            
            // Determine OS and set appropriate command
            String os = System.getProperty("os.name").toLowerCase();
            
            if (os.contains("win")) {
                command.add("cmd.exe");
                command.add("/c");
                command.add("start");
                command.add(appName);
            } else if (os.contains("mac")) {
                command.add("open");
                command.add("-a");
                command.add(appName);
            } else if (os.contains("nix") || os.contains("nux")) {
                command.add(appName);
            } else {
                return Response.builder()
                        .status(Response.Status.ERROR)
                        .message("Unsupported operating system for launching applications.")
                        .build();
            }
            
            // Add any additional parameters
            if (parameters != null && parameters.containsKey("args")) {
                String[] args = parameters.get("args").split("\\s+");
                for (String arg : args) {
                    command.add(arg);
                }
            }
            
            // Execute the command asynchronously
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            CompletableFuture.runAsync(() -> {
                try {
                    Process process = processBuilder.start();
                    process.waitFor();
                } catch (Exception e) {
                    log.error("Error launching application", e);
                }
            });
            
            return Response.builder()
                    .status(Response.Status.SUCCESS)
                    .message("Launching " + appName)
                    .build();
            
        } catch (Exception e) {
            log.error("Error launching application", e);
            return Response.builder()
                    .status(Response.Status.ERROR)
                    .message("Failed to launch " + appName + ": " + e.getMessage())
                    .build();
        }
    }

    /**
     * Runs a script with the given parameters.
     *
     * @param scriptPath The path to the script
     * @param parameters Additional parameters for the script
     * @return A response indicating the result of the operation
     */
    private Response runScript(String scriptPath, Map<String, String> parameters) {
        log.info("Running script: {}", scriptPath);
        
        try {
            List<String> command = new ArrayList<>();
            
            File script = new File(scriptPath);
            if (!script.exists()) {
                return Response.builder()
                        .status(Response.Status.ERROR)
                        .message("Script not found: " + scriptPath)
                        .build();
            }
            
            // Determine how to execute the script based on file extension
            String extension = scriptPath.substring(scriptPath.lastIndexOf('.') + 1).toLowerCase();
            
            switch (extension) {
                case "py":
                    command.add("python");
                    command.add(scriptPath);
                    break;
                case "sh":
                    command.add("bash");
                    command.add(scriptPath);
                    break;
                case "bat":
                case "cmd":
                    command.add("cmd.exe");
                    command.add("/c");
                    command.add(scriptPath);
                    break;
                default:
                    return Response.builder()
                            .status(Response.Status.ERROR)
                            .message("Unsupported script type: " + extension)
                            .build();
            }
            
            // Add any additional parameters
            if (parameters != null && parameters.containsKey("args")) {
                String[] args = parameters.get("args").split("\\s+");
                for (String arg : args) {
                    command.add(arg);
                }
            }
            
            // Execute the command and capture output
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            
            // Read the output
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            // Wait for the process to complete
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                return Response.builder()
                        .status(Response.Status.SUCCESS)
                        .message("Script executed successfully")
                        .data(output.toString())
                        .build();
            } else {
                return Response.builder()
                        .status(Response.Status.ERROR)
                        .message("Script execution failed with exit code: " + exitCode)
                        .data(output.toString())
                        .build();
            }
            
        } catch (IOException | InterruptedException e) {
            log.error("Error running script", e);
            return Response.builder()
                    .status(Response.Status.ERROR)
                    .message("Failed to run script: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Placeholder for reading emails.
     * In a real implementation, this would connect to an email service.
     *
     * @param parameters Parameters for reading emails (e.g., folder, count)
     * @return A response with email information
     */
    private Response readEmail(Map<String, String> parameters) {
        // This is a placeholder. In a real implementation, this would connect to an email service.
        return Response.builder()
                .status(Response.Status.SUCCESS)
                .message("You have 3 unread emails. The most recent one is from John regarding the project deadline.")
                .build();
    }

    /**
     * Placeholder for web search functionality.
     * In a real implementation, this would connect to a search engine API.
     *
     * @param parameters Search parameters (e.g., query)
     * @return A response with search results
     */
    private Response searchWeb(Map<String, String> parameters) {
        // This is a placeholder. In a real implementation, this would connect to a search engine API.
        String query = parameters != null ? parameters.getOrDefault("query", "unknown") : "unknown";
        return Response.builder()
                .status(Response.Status.SUCCESS)
                .message("Here are the top results for: " + query)
                .build();
    }

    /**
     * Gets basic system information.
     *
     * @return A response with system information
     */
    private Response getSystemInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Operating System: ").append(System.getProperty("os.name")).append(" ")
                .append(System.getProperty("os.version")).append("\n");
        info.append("Java Version: ").append(System.getProperty("java.version")).append("\n");
        info.append("Available Processors: ").append(Runtime.getRuntime().availableProcessors()).append("\n");
        info.append("Free Memory: ").append(Runtime.getRuntime().freeMemory() / (1024 * 1024)).append(" MB\n");
        info.append("Total Memory: ").append(Runtime.getRuntime().totalMemory() / (1024 * 1024)).append(" MB\n");
        
        return Response.builder()
                .status(Response.Status.SUCCESS)
                .message("Here is your system information")
                .data(info.toString())
                .build();
    }
}