package com.rishi.indu.controller;

import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import com.rishi.indu.service.CommandExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for the AI assistant.
 * Provides endpoints for receiving and processing commands.
 */
@RestController
@RequestMapping("/api/assistant")
@RequiredArgsConstructor
@Slf4j
public class AssistantController {

    private final CommandExecutor commandExecutor;

    /**
     * Endpoint for receiving and executing commands.
     *
     * @param command The command to execute
     * @return A response with the result of the command execution
     */
    @PostMapping("/command")
    public ResponseEntity<Response> executeCommand(@RequestBody Command command) {
        log.info("Received command: {}", command);
        Response response = commandExecutor.executeCommand(command);
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for checking if the assistant is alive.
     *
     * @return A simple response indicating the assistant is running
     */
    @GetMapping("/status")
    public ResponseEntity<Response> getStatus() {
        log.info("Status check");
        Response response = Response.builder()
                .status(Response.Status.SUCCESS)
                .message("I'm online and ready to assist you.")
                .build();
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for waking up the assistant.
     * This can be used by the wake word detection service.
     *
     * @return A response indicating the assistant is awake
     */
    @PostMapping("/wake")
    public ResponseEntity<Response> wake() {
        log.info("Wake command received");
        Response response = Response.builder()
                .status(Response.Status.SUCCESS)
                .message("I'm listening. How can I help you?")
                .build();
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for handling errors.
     *
     * @param message Error message
     * @return A response with the error message
     */
    @PostMapping("/error")
    public ResponseEntity<Response> reportError(@RequestParam String message) {
        log.error("Error reported: {}", message);
        Response response = Response.builder()
                .status(Response.Status.ERROR)
                .message("Error reported: " + message)
                .build();
        return ResponseEntity.ok(response);
    }
}