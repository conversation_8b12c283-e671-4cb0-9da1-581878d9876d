package com.rishi.indu.controller;

import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import com.rishi.indu.service.AssistantOrchestrationService;
import com.rishi.indu.service.CommandExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * REST controller for the AI assistant.
 * Provides endpoints for receiving and processing commands.
 */
@RestController
@RequestMapping("/api/assistant")
@RequiredArgsConstructor
@Slf4j
public class AssistantController {

    private final CommandExecutor commandExecutor;
    private final AssistantOrchestrationService orchestrationService;

    /**
     * Endpoint for receiving and executing commands.
     *
     * @param command The command to execute
     * @return A response with the result of the command execution
     */
    @PostMapping("/command")
    public ResponseEntity<Response> executeCommand(@RequestBody Command command) {
        log.info("Received command: {}", command);
        Response response = commandExecutor.executeCommand(command);
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for checking if the assistant is alive.
     *
     * @return A simple response indicating the assistant is running
     */
    @GetMapping("/status")
    public ResponseEntity<Response> getStatus() {
        log.info("Status check");
        Response response = Response.builder()
                .status(Response.Status.SUCCESS)
                .message("I'm online and ready to assist you.")
                .build();
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for waking up the assistant.
     * This can be used by the wake word detection service.
     *
     * @return A response indicating the assistant is awake
     */
    @PostMapping("/wake")
    public ResponseEntity<Response> wake() {
        log.info("Wake command received");
        Response response = orchestrationService.handleWakeWord();
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for processing text commands through the NLP pipeline.
     *
     * @param request Map containing the text command
     * @return A response with the result of the command execution
     */
    @PostMapping("/text")
    public Mono<ResponseEntity<Response>> processTextCommand(@RequestBody Map<String, String> request) {
        String text = request.get("text");
        if (text == null || text.trim().isEmpty()) {
            Response errorResponse = Response.builder()
                    .status(Response.Status.ERROR)
                    .message("Text parameter is required")
                    .build();
            return Mono.just(ResponseEntity.badRequest().body(errorResponse));
        }

        log.info("Processing text command: {}", text);

        return orchestrationService.processTextCommand(text)
                .map(ResponseEntity::ok)
                .onErrorResume(error -> {
                    log.error("Error processing text command", error);
                    Response errorResponse = Response.builder()
                            .status(Response.Status.ERROR)
                            .message("Error processing command: " + error.getMessage())
                            .build();
                    return Mono.just(ResponseEntity.status(500).body(errorResponse));
                });
    }

    /**
     * Endpoint for reporting errors from external services.
     *
     * @param message Error message
     * @return A response acknowledging the error report
     */
    @PostMapping("/error")
    public ResponseEntity<Response> reportError(@RequestParam String message) {
        log.error("Error reported: {}", message);
        Response response = Response.builder()
                .status(Response.Status.ERROR)
                .message("Error reported: " + message)
                .build();
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint for handling errors.
     *
     * @param message Error message
     * @return A response with the error message
     */
    @PostMapping("/error")
    public ResponseEntity<Response> reportError(@RequestParam String message) {
        log.error("Error reported: {}", message);
        Response response = Response.builder()
                .status(Response.Status.ERROR)
                .message("Error reported: " + message)
                .build();
        return ResponseEntity.ok(response);
    }
}