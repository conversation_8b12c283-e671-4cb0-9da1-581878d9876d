package com.rishi.indu.controller;

import com.rishi.indu.model.Response;
import com.rishi.indu.model.api.TtsResponse;
import com.rishi.indu.service.AssistantOrchestrationService;
import com.rishi.indu.service.AudioProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.Base64;
import java.util.Map;

/**
 * REST controller for handling audio-related operations.
 * Provides endpoints for voice commands and audio processing.
 */
@RestController
@RequestMapping("/api/audio")
@RequiredArgsConstructor
@Slf4j
public class AudioController {

    private final AssistantOrchestrationService orchestrationService;
    private final AudioProcessingService audioProcessingService;

    /**
     * Processes voice commands through the complete pipeline.
     * Accepts audio file upload and returns synthesized speech response.
     *
     * @param audioFile The uploaded audio file containing the voice command
     * @return ResponseEntity containing the audio response or error
     */
    @PostMapping(value = "/voice-command", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<byte[]>> processVoiceCommand(@RequestParam("audio") MultipartFile audioFile) {
        log.info("Received voice command audio file: {} (size: {} bytes)", 
                audioFile.getOriginalFilename(), audioFile.getSize());

        try {
            // Convert uploaded file to Base64
            String audioData = audioProcessingService.saveAndEncodeAudioFile(audioFile);
            String audioFormat = getAudioFormat(audioFile.getOriginalFilename());

            // Process through the complete pipeline
            return orchestrationService.processVoiceCommand(audioData, audioFormat)
                    .map(ttsResponse -> {
                        if (ttsResponse.getStatus() == TtsResponse.Status.SUCCESS) {
                            // Decode Base64 audio data
                            byte[] audioBytes = Base64.getDecoder().decode(ttsResponse.getAudioData());
                            
                            // Set appropriate headers
                            HttpHeaders headers = new HttpHeaders();
                            headers.setContentType(MediaType.parseMediaType("audio/" + ttsResponse.getAudioFormat()));
                            headers.setContentLength(audioBytes.length);
                            headers.set("Content-Disposition", "inline; filename=response." + ttsResponse.getAudioFormat());

                            return ResponseEntity.ok()
                                    .headers(headers)
                                    .body(audioBytes);
                        } else {
                            log.error("TTS processing failed: {}", ttsResponse.getErrorMessage());
                            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                    .body(("Error: " + ttsResponse.getErrorMessage()).getBytes());
                        }
                    })
                    .onErrorResume(error -> {
                        log.error("Error processing voice command", error);
                        return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(("Error processing voice command: " + error.getMessage()).getBytes()));
                    });

        } catch (IOException e) {
            log.error("Error processing audio file", e);
            return Mono.just(ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(("Error processing audio file: " + e.getMessage()).getBytes()));
        }
    }

    /**
     * Processes text commands and returns synthesized speech response.
     *
     * @param request Map containing the text command
     * @return ResponseEntity containing the audio response
     */
    @PostMapping(value = "/text-to-speech", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ResponseEntity<byte[]>> processTextToSpeech(@RequestBody Map<String, String> request) {
        String text = request.get("text");
        if (text == null || text.trim().isEmpty()) {
            return Mono.just(ResponseEntity.badRequest()
                    .body("Text parameter is required".getBytes()));
        }

        log.info("Processing text to speech: {}", text);

        return orchestrationService.processTextCommand(text)
                .flatMap(response -> {
                    // Convert response to TTS
                    return orchestrationService.processVoiceCommand("", "")
                            .map(ttsResponse -> {
                                if (ttsResponse.getStatus() == TtsResponse.Status.SUCCESS) {
                                    byte[] audioBytes = Base64.getDecoder().decode(ttsResponse.getAudioData());
                                    
                                    HttpHeaders headers = new HttpHeaders();
                                    headers.setContentType(MediaType.parseMediaType("audio/" + ttsResponse.getAudioFormat()));
                                    headers.setContentLength(audioBytes.length);

                                    return ResponseEntity.ok()
                                            .headers(headers)
                                            .body(audioBytes);
                                } else {
                                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                            .body(("TTS Error: " + ttsResponse.getErrorMessage()).getBytes());
                                }
                            });
                })
                .onErrorResume(error -> {
                    log.error("Error processing text to speech", error);
                    return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(("Error: " + error.getMessage()).getBytes()));
                });
    }

    /**
     * Simple endpoint to test audio upload functionality.
     *
     * @param audioFile The uploaded audio file
     * @return ResponseEntity with file information
     */
    @PostMapping(value = "/upload-test", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Map<String, Object>> testAudioUpload(@RequestParam("audio") MultipartFile audioFile) {
        log.info("Testing audio upload: {} (size: {} bytes)", 
                audioFile.getOriginalFilename(), audioFile.getSize());

        try {
            String audioFormat = getAudioFormat(audioFile.getOriginalFilename());
            
            Map<String, Object> response = Map.of(
                    "filename", audioFile.getOriginalFilename(),
                    "size", audioFile.getSize(),
                    "contentType", audioFile.getContentType(),
                    "detectedFormat", audioFormat,
                    "status", "success"
            );

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error testing audio upload", e);
            Map<String, Object> errorResponse = Map.of(
                    "status", "error",
                    "message", e.getMessage()
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Endpoint for wake word detection notifications.
     *
     * @return Response indicating the assistant is ready to listen
     */
    @PostMapping("/wake-word")
    public ResponseEntity<Response> handleWakeWord() {
        log.info("Wake word detected");
        Response response = orchestrationService.handleWakeWord();
        return ResponseEntity.ok(response);
    }

    /**
     * Extracts audio format from filename.
     *
     * @param filename The filename
     * @return Audio format (wav, mp3, etc.)
     */
    private String getAudioFormat(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "wav";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "wav";
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }
}
