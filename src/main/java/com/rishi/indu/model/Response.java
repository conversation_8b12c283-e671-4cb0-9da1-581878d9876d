package com.rishi.indu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a response from the assistant to a user command.
 * This model is used to transfer response data to the TTS service or directly to the client.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Response {
    
    /**
     * The status of the command execution
     */
    private Status status;
    
    /**
     * The text response to be spoken by the assistant
     */
    private String message;
    
    /**
     * Additional data that might be needed by the client
     */
    private Object data;
    
    /**
     * Enum representing different status types for command execution
     */
    public enum Status {
        SUCCESS,
        ERROR,
        IN_PROGRESS,
        WAITING_FOR_INPUT
    }
}