package com.rishi.indu.model.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request model for Speech-to-Text (STT) service.
 * Contains audio data to be transcribed.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SttRequest {
    
    /**
     * Base64 encoded audio data
     */
    private String audioData;
    
    /**
     * Audio format (e.g., wav, mp3)
     */
    private String audioFormat;
    
    /**
     * Language code (e.g., en-US)
     */
    private String languageCode;
}