package com.rishi.indu.model.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request model for Natural Language Processing (NLP) service.
 * Contains the text to be processed and analyzed.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NlpRequest {
    
    /**
     * The text to be processed
     */
    private String text;
    
    /**
     * The language code (e.g., en-US)
     */
    private String languageCode;
    
    /**
     * The context of the conversation (optional)
     * This can be used to maintain context across multiple interactions
     */
    private String context;
    
    /**
     * Whether to use GPT or another model for processing
     */
    private String model;
}