package com.rishi.indu.model.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request model for Text-to-Speech (TTS) service.
 * Contains the text to be converted to speech.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TtsRequest {
    
    /**
     * The text to be converted to speech
     */
    private String text;
    
    /**
     * The language code (e.g., en-US)
     */
    private String languageCode;
    
    /**
     * The voice to use (e.g., male, female, specific voice name)
     */
    private String voice;
    
    /**
     * The speaking rate (1.0 is normal speed, 0.5 is half speed, 2.0 is double speed)
     */
    private double speakingRate;
    
    /**
     * The pitch (0.0 is normal pitch, -10.0 to 10.0)
     */
    private double pitch;
    
    /**
     * The audio format to return (e.g., mp3, wav)
     */
    private String audioFormat;
}