package com.rishi.indu.model.api;

import com.rishi.indu.model.Command;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Response model from Natural Language Processing (NLP) service.
 * Contains the parsed command and additional information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NlpResponse {
    
    /**
     * The parsed command type
     */
    private Command.CommandType commandType;
    
    /**
     * The target of the command (e.g., application name, script name)
     */
    private String target;
    
    /**
     * Additional parameters for the command
     */
    private Map<String, String> parameters;
    
    /**
     * The original text that was processed
     */
    private String originalText;
    
    /**
     * Confidence score of the parsing (0.0 to 1.0)
     */
    private double confidence;
    
    /**
     * Status of the NLP processing
     */
    private Status status;
    
    /**
     * Error message if status is ERROR
     */
    private String errorMessage;
    
    /**
     * Response text to be spoken back to the user
     */
    private String responseText;
    
    /**
     * Enum representing the status of the NLP processing
     */
    public enum Status {
        SUCCESS,
        ERROR,
        AMBIGUOUS
    }
    
    /**
     * Converts this NLP response to a Command object
     * 
     * @return A Command object
     */
    public Command toCommand() {
        return Command.builder()
                .type(commandType)
                .target(target)
                .parameters(parameters)
                .originalText(originalText)
                .build();
    }
}