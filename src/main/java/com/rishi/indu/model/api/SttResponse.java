package com.rishi.indu.model.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response model from Speech-to-Text (STT) service.
 * Contains the transcribed text and confidence score.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SttResponse {
    
    /**
     * The transcribed text from the audio
     */
    private String text;
    
    /**
     * Confidence score of the transcription (0.0 to 1.0)
     */
    private double confidence;
    
    /**
     * Status of the transcription
     */
    private Status status;
    
    /**
     * Error message if status is ERROR
     */
    private String errorMessage;
    
    /**
     * Enum representing the status of the transcription
     */
    public enum Status {
        SUCCESS,
        ERROR
    }
}