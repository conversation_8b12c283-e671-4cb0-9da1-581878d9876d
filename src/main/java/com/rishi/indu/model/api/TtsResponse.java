package com.rishi.indu.model.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response model from Text-to-Speech (TTS) service.
 * Contains the generated audio data.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TtsResponse {
    
    /**
     * Base64 encoded audio data
     */
    private String audioData;
    
    /**
     * Audio format (e.g., mp3, wav)
     */
    private String audioFormat;
    
    /**
     * Duration of the audio in seconds
     */
    private double durationInSeconds;
    
    /**
     * Status of the TTS processing
     */
    private Status status;
    
    /**
     * Error message if status is ERROR
     */
    private String errorMessage;
    
    /**
     * Enum representing the status of the TTS processing
     */
    public enum Status {
        SUCCESS,
        ERROR
    }
}