package com.rishi.indu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Represents a command parsed from user speech input.
 * This model is used to transfer command data from the NLP service to the command executor.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Command {
    
    /**
     * The type of command (e.g., LAUNCH_APP, RUN_SCRIPT, READ_EMAIL, etc.)
     */
    private CommandType type;
    
    /**
     * The primary target of the command (e.g., application name, script name)
     */
    private String target;
    
    /**
     * Additional parameters for the command
     */
    private Map<String, String> parameters;
    
    /**
     * The original text of the command as parsed from speech
     */
    private String originalText;
    
    /**
     * Enum representing different types of commands the assistant can handle
     */
    public enum CommandType {
        LAUNCH_APP,
        RUN_SCRIPT,
        READ_EMAIL,
        SEARCH_WEB,
        SYSTEM_INFO,
        UNKNOWN
    }
}