package com.rishi.indu.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Configuration class for WebClient beans.
 * Provides configured WebClient instances for different services.
 */
@Configuration
public class WebClientConfiguration {

    /**
     * Creates a WebClient bean for STT service.
     *
     * @return WebClient configured for STT service
     */
    @Bean("sttWebClient")
    public WebClient sttWebClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB for audio files
                .build();
    }

    /**
     * Creates a WebClient bean for NLP service.
     *
     * @return WebClient configured for NLP service
     */
    @Bean("nlpWebClient")
    public WebClient nlpWebClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)) // 1MB for text
                .build();
    }

    /**
     * Creates a WebClient bean for TTS service.
     *
     * @return WebClient configured for TTS service
     */
    @Bean("ttsWebClient")
    public WebClient ttsWebClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB for audio files
                .build();
    }

    /**
     * Creates a general WebClient bean.
     *
     * @return General purpose WebClient
     */
    @Bean
    public WebClient webClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024)) // 16MB
                .build();
    }
}
