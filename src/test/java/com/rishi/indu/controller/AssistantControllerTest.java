package com.rishi.indu.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import com.rishi.indu.service.CommandExecutor;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Test class for AssistantController.
 * Tests the REST API endpoints for the assistant.
 */
@SpringBootTest
@AutoConfigureMockMvc
class AssistantControllerTest {

    @TestConfiguration
    static class TestConfig {
        @Bean
        @Primary
        public CommandExecutor commandExecutor() {
            return Mockito.mock(CommandExecutor.class);
        }
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CommandExecutor commandExecutor;

    @Test
    void testStatusEndpoint() throws Exception {
        mockMvc.perform(get("/api/assistant/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("I'm online and ready to assist you."));
    }

    @Test
    void testWakeEndpoint() throws Exception {
        mockMvc.perform(post("/api/assistant/wake"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("I'm listening. How can I help you?"));
    }

    @Test
    void testExecuteCommandEndpoint() throws Exception {
        // Create a command
        Map<String, String> parameters = new HashMap<>();
        parameters.put("query", "Spring Boot tutorial");

        Command command = Command.builder()
                .type(Command.CommandType.SEARCH_WEB)
                .target("web")
                .parameters(parameters)
                .originalText("Search the web for Spring Boot tutorial")
                .build();

        // Mock the command executor response
        Response mockResponse = Response.builder()
                .status(Response.Status.SUCCESS)
                .message("Here are the top results for: Spring Boot tutorial")
                .build();

        when(commandExecutor.executeCommand(any(Command.class))).thenReturn(mockResponse);

        // Perform the request
        mockMvc.perform(post("/api/assistant/command")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("Here are the top results for: Spring Boot tutorial"));
    }

    @Test
    void testErrorEndpoint() throws Exception {
        String errorMessage = "Test error message";

        mockMvc.perform(post("/api/assistant/error")
                .param("message", errorMessage))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("ERROR"))
                .andExpect(jsonPath("$.message").value("Error reported: " + errorMessage));
    }
}