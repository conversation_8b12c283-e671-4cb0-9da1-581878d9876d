package com.rishi.indu.service;

import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for CommandExecutor.
 * Tests the execution of different types of commands.
 */
@SpringBootTest
class CommandExecutorTest {

    private CommandExecutor commandExecutor;

    @BeforeEach
    void setUp() {
        commandExecutor = new CommandExecutor();
    }

    @Test
    void testSystemInfoCommand() {
        // Create a SYSTEM_INFO command
        Command command = Command.builder()
                .type(Command.CommandType.SYSTEM_INFO)
                .originalText("Show me system information")
                .build();

        // Execute the command
        Response response = commandExecutor.executeCommand(command);

        // Verify the response
        assertNotNull(response);
        assertEquals(Response.Status.SUCCESS, response.getStatus());
        assertNotNull(response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData().toString().contains("Operating System"));
    }

    @Test
    void testUnknownCommand() {
        // Create an UNKNOWN command
        Command command = Command.builder()
                .type(Command.CommandType.UNKNOWN)
                .originalText("This is an unknown command")
                .build();

        // Execute the command
        Response response = commandExecutor.executeCommand(command);

        // Verify the response
        assertNotNull(response);
        assertEquals(Response.Status.ERROR, response.getStatus());
        assertTrue(response.getMessage().contains("don't understand"));
    }

    @Test
    void testSearchWebCommand() {
        // Create a SEARCH_WEB command
        Map<String, String> parameters = new HashMap<>();
        parameters.put("query", "Spring Boot tutorial");

        Command command = Command.builder()
                .type(Command.CommandType.SEARCH_WEB)
                .parameters(parameters)
                .originalText("Search the web for Spring Boot tutorial")
                .build();

        // Execute the command
        Response response = commandExecutor.executeCommand(command);

        // Verify the response
        assertNotNull(response);
        assertEquals(Response.Status.SUCCESS, response.getStatus());
        assertTrue(response.getMessage().contains("Spring Boot tutorial"));
    }

    @Test
    void testReadEmailCommand() {
        // Create a READ_EMAIL command
        Command command = Command.builder()
                .type(Command.CommandType.READ_EMAIL)
                .originalText("Read my emails")
                .build();

        // Execute the command
        Response response = commandExecutor.executeCommand(command);

        // Verify the response
        assertNotNull(response);
        assertEquals(Response.Status.SUCCESS, response.getStatus());
        assertTrue(response.getMessage().contains("unread emails"));
    }
}