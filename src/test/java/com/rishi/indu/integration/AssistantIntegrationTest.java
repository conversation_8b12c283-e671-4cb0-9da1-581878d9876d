package com.rishi.indu.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rishi.indu.model.Command;
import com.rishi.indu.model.Response;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the AI Assistant application.
 * Tests the complete flow of the assistant endpoints.
 */
@SpringBootTest
@AutoConfigureMockMvc
class AssistantIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testAssistantStatusEndpoint() throws Exception {
        mockMvc.perform(get("/api/assistant/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    void testWakeEndpoint() throws Exception {
        mockMvc.perform(post("/api/assistant/wake"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    void testDirectCommandExecution() throws Exception {
        // Test SYSTEM_INFO command
        Command command = Command.builder()
                .type(Command.CommandType.SYSTEM_INFO)
                .originalText("show system information")
                .build();

        mockMvc.perform(post("/api/assistant/command")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    void testLaunchAppCommand() throws Exception {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("args", "");

        Command command = Command.builder()
                .type(Command.CommandType.LAUNCH_APP)
                .target("notepad")
                .parameters(parameters)
                .originalText("open notepad")
                .build();

        mockMvc.perform(post("/api/assistant/command")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"));
    }

    @Test
    void testSearchWebCommand() throws Exception {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("query", "Spring Boot tutorial");

        Command command = Command.builder()
                .type(Command.CommandType.SEARCH_WEB)
                .target("web")
                .parameters(parameters)
                .originalText("search for Spring Boot tutorial")
                .build();

        mockMvc.perform(post("/api/assistant/command")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("Here are the top results for: Spring Boot tutorial"));
    }

    @Test
    void testReadEmailCommand() throws Exception {
        Command command = Command.builder()
                .type(Command.CommandType.READ_EMAIL)
                .originalText("read my emails")
                .build();

        mockMvc.perform(post("/api/assistant/command")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("You have 3 unread emails. The most recent one is from John regarding the project deadline."));
    }

    @Test
    void testUnknownCommand() throws Exception {
        Command command = Command.builder()
                .type(Command.CommandType.UNKNOWN)
                .originalText("do something impossible")
                .build();

        mockMvc.perform(post("/api/assistant/command")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("ERROR"))
                .andExpect(jsonPath("$.message").value("I'm sorry, I don't understand that command."));
    }

    @Test
    void testTextCommandWithValidInput() throws Exception {
        Map<String, String> request = new HashMap<>();
        request.put("text", "show system information");

        // Note: This test will fail if NLP service is not running
        // In a real integration test, you might want to mock the external services
        mockMvc.perform(post("/api/assistant/text")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }

    @Test
    void testTextCommandWithEmptyInput() throws Exception {
        Map<String, String> request = new HashMap<>();
        request.put("text", "");

        mockMvc.perform(post("/api/assistant/text")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value("ERROR"))
                .andExpect(jsonPath("$.message").value("Text parameter is required"));
    }

    @Test
    void testTextCommandWithMissingInput() throws Exception {
        Map<String, String> request = new HashMap<>();
        // No text parameter

        mockMvc.perform(post("/api/assistant/text")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value("ERROR"))
                .andExpect(jsonPath("$.message").value("Text parameter is required"));
    }

    @Test
    void testErrorReportingEndpoint() throws Exception {
        String errorMessage = "Test error message";

        mockMvc.perform(post("/api/assistant/error")
                .param("message", errorMessage))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("ERROR"))
                .andExpect(jsonPath("$.message").value("Error reported: " + errorMessage));
    }
}
