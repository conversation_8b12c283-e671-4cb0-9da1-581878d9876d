#!/bin/bash

# Test script for JARVIS AI Assistant

echo "🤖 JARVIS AI Assistant Test Suite"
echo "================================="

BASE_URL="http://localhost:8080/indu"
PYTHON_SERVICES_BASE="http://localhost"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test HTTP endpoint
test_endpoint() {
    local name=$1
    local url=$2
    local method=${3:-GET}
    local data=$4
    
    echo -n "Testing $name... "
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "$url")
    else
        response=$(curl -s -w "%{http_code}" "$url")
    fi
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✓ PASS${NC}"
        return 0
    else
        echo -e "${RED}✗ FAIL (HTTP $http_code)${NC}"
        echo "  Response: $body"
        return 1
    fi
}

echo ""
echo "1. Testing Python Microservices Health"
echo "-------------------------------------"

test_endpoint "STT Service Health" "$PYTHON_SERVICES_BASE:5000/health"
test_endpoint "NLP Service Health" "$PYTHON_SERVICES_BASE:5001/health"
test_endpoint "TTS Service Health" "$PYTHON_SERVICES_BASE:5002/health"

echo ""
echo "2. Testing Java Spring Boot Application"
echo "--------------------------------------"

test_endpoint "Assistant Status" "$BASE_URL/api/assistant/status"
test_endpoint "Assistant Wake" "$BASE_URL/api/assistant/wake" "POST"

echo ""
echo "3. Testing Text Command Processing"
echo "---------------------------------"

test_endpoint "System Info Command" "$BASE_URL/api/assistant/text" "POST" '{"text": "show system information"}'
test_endpoint "Launch App Command" "$BASE_URL/api/assistant/text" "POST" '{"text": "open Chrome"}'
test_endpoint "Web Search Command" "$BASE_URL/api/assistant/text" "POST" '{"text": "search for Python tutorials"}'

echo ""
echo "4. Testing Direct Command Execution"
echo "----------------------------------"

# Test direct command execution
command_data='{
    "type": "SYSTEM_INFO",
    "target": "",
    "parameters": {},
    "originalText": "show system info"
}'

test_endpoint "Direct Command Execution" "$BASE_URL/api/assistant/command" "POST" "$command_data"

echo ""
echo "5. Testing Python Services Directly"
echo "-----------------------------------"

# Test STT service (without actual audio)
echo -n "Testing STT Service... "
stt_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"audioData": "", "audioFormat": "wav", "languageCode": "en-US"}' \
    "$PYTHON_SERVICES_BASE:5000/api/transcribe")
if echo "$stt_response" | grep -q "status"; then
    echo -e "${GREEN}✓ PASS${NC}"
else
    echo -e "${RED}✗ FAIL${NC}"
fi

# Test NLP service
nlp_data='{"text": "open Chrome browser", "languageCode": "en-US", "model": "gpt-3.5-turbo"}'
test_endpoint "NLP Service" "$PYTHON_SERVICES_BASE:5001/api/parse" "POST" "$nlp_data"

# Test TTS service
tts_data='{"text": "Hello, I am JARVIS", "languageCode": "en-US", "voice": "default", "audioFormat": "mp3"}'
test_endpoint "TTS Service" "$PYTHON_SERVICES_BASE:5002/api/synthesize" "POST" "$tts_data"

echo ""
echo "6. Testing Service Discovery"
echo "---------------------------"

test_endpoint "STT Models" "$PYTHON_SERVICES_BASE:5000/api/models"
test_endpoint "NLP Models" "$PYTHON_SERVICES_BASE:5001/api/models"
test_endpoint "TTS Voices" "$PYTHON_SERVICES_BASE:5002/api/voices"

echo ""
echo "🎯 Test Summary"
echo "==============="

# Count running services
services_running=0
if curl -s "$PYTHON_SERVICES_BASE:5000/health" > /dev/null 2>&1; then
    ((services_running++))
fi
if curl -s "$PYTHON_SERVICES_BASE:5001/health" > /dev/null 2>&1; then
    ((services_running++))
fi
if curl -s "$PYTHON_SERVICES_BASE:5002/health" > /dev/null 2>&1; then
    ((services_running++))
fi

echo "Python Services Running: $services_running/3"

if curl -s "$BASE_URL/api/assistant/status" > /dev/null 2>&1; then
    echo -e "Java Application: ${GREEN}✓ Running${NC}"
else
    echo -e "Java Application: ${RED}✗ Not Running${NC}"
fi

echo ""
echo "📝 Next Steps:"
echo "1. If services are not running, start them with:"
echo "   cd python-services && ./start-all-services.sh"
echo "   mvn spring-boot:run"
echo ""
echo "2. Set your OpenAI API key:"
echo "   export OPENAI_API_KEY='your-api-key-here'"
echo ""
echo "3. Test voice commands by uploading audio files to:"
echo "   POST $BASE_URL/api/audio/voice-command"
echo ""
echo "4. For full voice interaction, implement wake word detection"
echo "   and continuous audio streaming."
